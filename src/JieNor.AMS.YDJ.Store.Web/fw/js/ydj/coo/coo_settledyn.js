///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/coo/coo_settledyn.js
*/
;(function () {
    var coo_settledyn = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            //所有可用于订单支付的账户信息
            that.accounts = [];

            //合同携带过来的退款金额绝对值
            that.orderfamount = 0;

            //当前登录经销商经营模式
            that.fmanagemodel = '';

            that.fsettletype = "";
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        //联合开单信息
        _child.prototype.dutyEntryId = 'fdutyentry';
        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //处理表单渲染事件：可以在此方法里，对页面做动态化渲染处理
        _child.prototype.onPageViewRendering = function () {
            var that = this;

            //生成业务字段视图
            var bizFields = that.formContext.cp.bizFields;
            if (bizFields && bizFields.length > 0) {
                bizFields.push({id: 'fsettleamount', cation: '本次结算额'});
                var html = '';
                for (var i = 0; i < bizFields.length; i++) {
                    if (!bizFields[i].id || !bizFields[i].cation) continue;
                    var fieldVal = that.formContext.cp[bizFields[i].id];
                    if (bizFields[i].id === 'fsettleamount') fieldVal = '0.00';
                    if (fieldVal === undefined) fieldVal = '&nbsp;';
                    html += '\
                    <div class="col-md-6">\
                        <div class="form-group">\
                            <label class="col-md-4 control-label">{0} :</label>\
                            <div class="col-md-8">\
                                <p class="form-control-static {2}">{1}</p>\
                            </div>\
                        </div>\
                    </div>'.format(bizFields[i].cation, fieldVal, bizFields[i].id);
                }
                that.Model.setHtml({id: '.ydj-order-settle-static', value: html});
            }
        };

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
         
            that.HidePaymentdesc();
            var cp = that.formContext.cp;
            if (!cp) return;
            debugger
            that.fsettletype = cp.fsettletype;
            that.renewalHeadquart();
            that.Model.setValue({id: 'fdeptid', value: cp.fdeptid});
            that.Model.setValue({id: 'fstaffid', value: cp.fstaffid});

            if (cp.fcontactunittype) {
                that.Model.setValue({id: 'fcontactunittype', value: cp.fcontactunittype});
                if (cp.fcontactunittype.id == 'contactunittype_02') {
                    that.Model.setVisible({id: '.marketsettleno', value: true});
                }
            }

            if (cp.fcontactunitid) {
                that.Model.setValue({id: 'fcontactunitid', value: cp.fcontactunitid});
            }

            if (cp.fisunifiedcashier) {
                that.Model.setEnable({"id": "fcontactunitid", value: false});
                that.Model.setEnable({"id": "fcontactunittype", value: false});
            }

            //修改对话框标题
            that.Model.getEleMent({id: '.form-horizontal'})
                .closest('.layui-layer-content')
                .siblings('.layui-layer-title')
                .text(cp.fsettletype);
            if ((cp.fsettletype && cp.fsettletype == '收款') || (cp.fsourceformid === 'ydj_order' && cp.fsettletype && cp.fsettletype == '退款')) {
                that.Model.setVisible({id: '.receiptno', value: true});
            } else {
                that.Model.setVisible({id: '.receiptno', value: false});
            }

            //用途
            //var purpose = $.trim(cp.fpurpose).toLowerCase();
            //switch (purpose) {
            //    case 'bizpurpose_06':
            //        that.Model.setVisible({ id: '.fcontactunitid-box', value: false });
            //        break;
            //}

            //显示对方银行账号（如果是协同结算 或者 是费用申请单结算，则显示对方银行）
            if (cp.fissyn || cp.fsourceformid === 'ste_registfee') {
                if (cp.synBankNum && cp.synBankNum.length > 0) {
                    var bankComboData = [{id: '', name: '&nbsp;'}];
                    for (var i = 0; i < cp.synBankNum.length; i++) {
                        var bank = cp.synBankNum[i];
                        if (!$.trim(bank.accountName)) bank.accountName = '空';
                        if (!$.trim(bank.bankName)) bank.bankName = '空';
                        bankComboData.push({
                            id: bank.accountId,
                            name: '{0} - {1} - {2}'.format(bank.bankNum, bank.accountName, bank.bankName)
                        });
                    }
                    //***************** - 宋纪强 - 招商银行
                    that.Model.setComboData({id: 'fsynbankid', data: bankComboData});
                }
                that.Model.setVisible({id: '.syn-banknum', value: true});
            }

            that.SetStkReturnValue(cp);

            //初始化账户数据源
            var fallaccounts = cp.fallaccounts
            if (fallaccounts && fallaccounts.length > 0) {
                //下拉框数据源
                var accountData = [];
                for (var i = 0; i < fallaccounts.length; i++) {
                    var account = fallaccounts[i];
                    //是否可用于订单支付
                    if (account.canUseInOrderPay) {
                        that.accounts.push({
                            accountId: account.accountId,
                            accountName: account.accountName,
                            balance: account.balance,
                            canRecharge: account.canRecharge
                        });
                        accountData.push({
                            id: account.accountId,
                            name: account.accountName
                        });
                    }
                }
                that.Model.setComboData({id: 'faccount', data: accountData});
                if (accountData.length > 0) {
                    that.Model.setValue({"id": "faccount", value: accountData[0]});
                } else {
                    var wayDatas = that.Model.viewModel.uiComboData.fway;
                    if (wayDatas && wayDatas.length > 0) {
                        var cloneDatas = wayDatas.map(function (x) {
                            return {"id": x.id, "name": x.name, number: x.number};
                        });
                        var payData = cloneDatas.find(function (x) {
                            return "payway_01" == x.id
                        });
                        if (payData) {
                            payData.disable = true;
                        }
                        that.Model.setComboData({id: 'fway', data: cloneDatas});
                        var payData = cloneDatas.find(function (x) {
                            return "payway_04" == x.id
                        });
                        if (!payData) {
                            payData = cloneDatas[0];
                        }
                        that.Model.setValue({"id": "fway", "value": payData});
                    }
                }
            }

            //获取门店系统参数本地缓存数据
            var param = JSON.parse(localStorage.getItem("storesysparam"));
            if (!param) {
                //本地缓存不存在则请求
                that.Model.invokeFormOperation({
                    id: 'loadstoresysparam',
                    opcode: 'loadstoresysparam',
                    param: {
                        'formId': 'bas_storesysparam',
                        'domainType': 'parameter'
                    }
                });
            }
            that.procSaleManOp(cp);
            that.HideCusAcount();

            //$(".receiptno").bind("keyup", function (e) {
            //    //that.Model.setValue({ id: "freceiptno", value: this.value.replace(/[^0-9A-Za-z]+$/, '') });
            //    //平台方法会触发值改变事件，放弃使用
            //    $(".receiptno").val(this.value.replace(/[^0-9A-Za-z]+$/, ''));
            //});
            //销售合同
            if (cp.amount > 0 && cp.fsourceformid == "ydj_order") {
                that.orderfamount = cp.amount;
                that.Model.setValue({id: 'famount', value: cp.amount});
                that.Model.setValue({id: 'fway', value: "payway_01"});
                that.Model.setValue({id: 'paymentdesc', value: cp.paymentdesc});
            }
            //其他应收单
            if (cp.amount > 0 && cp.fsourceformid == "ydj_collectreceipt") {
                that.Model.setValue({id: 'famount', value: cp.amount});
                that.Model.setValue({id: 'paymentdesc', value: cp.paymentdesc});
            }
            that.hideOrShowDutyEntryField(that.dutyEntryId, 'fdeptperfratio');
        };

        //是否总部账号
        _child.prototype.GetAgentInfo = function () {
            var that = this;
            var companyid = Consts.loginCompany.id;
            var fstatus = that.Model.getSimpleValue({id: 'fstatus'});
            var fmainorgid = that.Model.getSimpleValue({id: 'fmainorgid'});
            if (fstatus != '') {
                companyid = fmainorgid;
            }
            that.Model.invokeFormOperation({
                id: 'getagentinfo',
                opcode: 'getagentinfo',
                param: {
                    formId: 'bas_agent',
                    companyid: companyid
                }
            });
        }

        //总部相关字段 当【登录账号所属经销商.经营类型=直营】默认可见，反之不显示
        _child.prototype.renewalHeadquart = function () {
            debugger
            var that = this;
            var enable = true;
            var paymentEnable = false;//收款
            var wayEnable = false;
            //直营
            if (Consts.isdirectsale) {
                if (that.fsettletype=== "退款"){
                    enable = true;
                    paymentEnable = false;

                } else if (that.fsettletype === "收款") {
                    enable = false;
                    paymentEnable = true;
                }
                wayEnable = false;
            } else {
                enable = false;
                paymentEnable = false;
                wayEnable = true;
            }
            that.Model.setVisible({id: '.renewal-info-head', value: !enable});
            that.Model.setVisible({id: '.renewal-info-head-refund', value: enable});
            that.Model.setVisible({id: '.renewal-info-head-payment', value: paymentEnable});
            that.Model.setVisible({id: '.renewal-info-head-way', value: wayEnable});

            that.renewalHeadquartAttr();
        }

        _child.prototype.renewalHeadquartAttr = function () {
            var that = this;
            if (Consts.isdirectsale) {
                that.setFieldMustFlag({id: "frefundway", caption: "退款方式", must: true});
                that.setFieldMustFlag({id: "frefundwaydesc", caption: "退款说明", must: true});
                that.setFieldMustFlag({id: "fdescription", caption: "退款原因", must: true});
            } else {
                that.setFieldMustFlag({id: "frefundway", caption: "退款方式", must: false});
                that.setFieldMustFlag({id: "frefundwaydesc", caption: "退款说明", must: false});
                that.setFieldMustFlag({id: "fdescription", caption: "退款原因", must: false});
            }
        }

        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            debugger
            var that = this;
            var elem = that.Model.getEleMent({id: '[name=' + e.id + ']'});
            if (elem) {
                var $label = elem.parent().parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({id: e.id, random: 'required', value: 'required', way: 2});
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({id: e.id, random: 'required', value: 'required', way: 2});
                        $label.html(e.caption);
                    }
                }
            }
        };

        _child.prototype.HidePaymentdesc = function () {
            var paynum = $("select[name='paymentdesc']").find('option').length;
            if (paynum < 1) {
                $(".hidepaycs").hide();
                $("select[name='paymentdesc']").attr('required', false)
            }
        }

        //显示或隐藏客户收款账号
        _child.prototype.HideCusAcount = function (cp) {
            var that = this;
            var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
            var frefundcusacount = storeParam ? storeParam.frefundcusacount : false;
            var fsettletype = $.trim(that.Model.getValue({id: 'fsettletype'}));
            var fsettlemaintype = $.trim(that.Model.getValue({id: 'fsettlemaintype'}));
            var fway = that.Model.getValue({id: 'fway'});
            var fpaymentway = that.Model.getValue({id: 'fpaymentway'});
            if (Consts.isdirectsale){
                fway = fpaymentway;
            }
            
            //当启用参数退款时客户收款账号必录且退款且结算主体为客户时，需自动将《客户》的【银行卡号】携带到【客户收款账号】中显示
            if (frefundcusacount && fsettletype == "退款" && fsettlemaintype == "ydj_customer" && fway.id != "payway_01") {
                that.Model.setVisible({id: '.cusacount', value: true});
                var fbanknumber = that.GetCusacount(that.Model.getValue({id: 'fsettlemainid'}));
                that.Model.setValue({"id": "fcusacount", "value": fbanknumber});
            } else {
                that.Model.setVisible({id: '.cusacount', value: false});
            }

        };

        //显示或隐藏联合开单信息
        _child.prototype.procSaleManOp = function (cp) {
            var that = this;
            if (cp.fsourceformid === 'ydj_order' || cp.fsourceformid === 'ydj_collectreceipt') {
                that.Model.setVisible({id: '.dutyentry', value: true});
                that.DealDutyEntry(true);
            } else {
                that.Model.setVisible({id: '.dutyentry', value: false});
            }


        };

        //销售退货单相关值设置
        _child.prototype.SetStkReturnValue = function (cp) {
            var that = this;
            if (cp.fsourceformid == "stk_sostockreturn") {
                if (Consts.isdirectsale){
                    that.Model.setValue({"id": "fpaymentway", "value": cp.fway});//默认账户支付
                }
                else {
                    that.Model.setValue({"id": "fway", "value": cp.fway});//默认账户支付
                }
                that.Model.setValue({"id": "famount", "value": cp.fsettleamount});//结算金额
            }
        }

        //表单字段标签点击后触发
        _child.prototype.onFieldLabelClick = function (e) {
            switch (e.id.toLowerCase()) {
                case 'fcontactunitid':
                    //辅助资料没有单独的视图文件，不允许点击标签打开，所以这里取消平台标准通用逻辑
                    e.cancel = true;
                    break;
            }
        };

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            if (e.id === 'famount') {
                var famount = $.trim(e.value).split('.');
                if (famount && famount.length === 2 && famount[1].length > 2) {
                    e.value = yiMath.toNumber(yiMath.toDecimal(e.value, 2));
                    e.result = true;
                    yiDialog.mt({msg: '结算金额只能输入两位小数点！', skinseq: 2});
                }

                var settledAmount = yiMath.toNumber(that.Model.getValue({id: 'fsettledamount'}));

                e.value = yiMath.toNumber(e.value);

                //销售退货单忽略金额判断
                if (that.formContext.cp.fsourceformid == "stk_sostockreturn" || that.formContext.cp.fsourceformid == "ydj_collectreceipt") return;
                //如果非付款且e.value > settledAmount
                if (that.formContext.cp.fpurpose != 'bizpurpose_02' && e.value > settledAmount) {
                    e.value = settledAmount;
                    e.result = true;
                    yiDialog.mt({msg: '由于输入的金额不满足当前支付条件已自动更新！', skinseq: 2});
                }
            }
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fmybankid':
                    debugger
                    var frefundway = that.Model.getSimpleValue({id: 'frefundway'});
                    //银行转账
                    if (frefundway === 'payway_06') {
                        var filter = "fisrefundbanknum='1'";
                        var fsettlemainid = that.Model.getValue({id: 'fsettlemainid'});
                        if (fsettlemainid) {
                            filter += "and fid in (select fid from t_ydj_bankcustentry where fcustomerid=('" + fsettlemainid + "'))";
                        }
                        e.result.filterString = filter;
                    }
                    break;
            }
        }

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            //汇总本次计算额
            if (e.id === 'famount' || e.id.indexOf('settleaccount_type') != -1) {
                var sumAmount = yiMath.toNumber(that.Model.getValue({id: 'famount'}));
                for (var i = 0; i < that.accounts.length; i++) {
                    sumAmount += yiMath.toNumber(that.Model.getValue({id: that.accounts[i].accountId}));
                }
                that.Model.setText({id: '.fsettleamount', value: yiMath.toDecimal(sumAmount, 2)});
                that.Model.setValue({id: 'fsettleamount', value: yiMath.toDecimal(sumAmount, 2)});
            }

            switch (e.id) {
                //退款说明
                case "frefundwaydesc":
                    debugger
                    that.Model.setValue({id: 'paymentdesc', value: e.value.id});
                    break;
                case "frefundway":
                    //银行转账-可编辑【银行账号】
                    if (e.value.id == 'payway_06') {
                        that.Model.setEnable({id: "fmybankid", value: true});
                    } else {
                        that.Model.setEnable({id: "fmybankid", value: false});
                    }
                    that.Model.setValue({id: 'fway', value: e.value.id});
                    break;
                case "fway":
                case "fpaymentway":
                    if (e.value.id != "payway_06" &&
                        e.value.id != "payway_07" &&
                        e.value.id != "payway_08" &&
                        e.value.id != "payway_11") {
                        that.Model.setValue({id: "fmybankid", value: ""});
                        that.Model.setValue({id: "fsynbankid", value: ""});
                        that.Model.setValue({id: "fsynbankname", value: ""});
                        that.Model.setValue({id: "fsynbanknum", value: ""});
                        that.Model.setValue({id: "fsynaccountname", value: ""});
                    }
                    that.Model.setValue({id: 'fotherpartyaccount', value: ""});
                    that.HideCusAcount();
                    break;
                case 'faccount':
                    var accountId = $.trim(e.value.id);
                    var balance = 0;
                    var topup = false;
                    if (accountId && that.accounts && that.accounts.length > 0) {
                        for (var i = 0; i < that.accounts.length; i++) {
                            if (that.accounts[i].accountId === accountId) {
                                balance = that.accounts[i].balance;
                                topup = that.accounts[i].canRecharge;
                                break;
                            }
                        }
                    }
                    that.Model.setHtml({id: '.y-account-balance', value: '余额 ' + yiMath.toDecimal(balance, 2)});
                    //that.Model.setVisible({ id: '.y-account-topup', value: topup });
                    that.Model.setAttr({
                        id: '.y-account-topup',
                        random: 'data-param',
                        value: "accountId:'" + accountId + "'"
                    });
                    break;
                case 'famount':
                    var cp = that.formContext.cp;
                    if (cp) {
                        if (cp.fsourceformid == "ydj_order") {
                            var famount = that.Model.getValue({id: 'famount'})
                            if (that.orderfamount && that.orderfamount < famount) {
                                yiDialog.warn('对不起，你输入的金额大于了应退金额，请检查！');
                                setTimeout(function () {
                                    that.Model.setValue({id: 'famount', value: that.orderfamount});
                                }, 100);

                            }
                        }
                    }
                    that.calculateStaff();
                case 'fratio':
                case 'famount_ed':
                    that.calculateStaff(e);
                    break;
                case 'fdutyid':
                    //将销售员部门自动填充到人员明细表格中
                    var fdeptid = that.GetDeptByStaff(e.value.id);
                    if (fdeptid && fdeptid != "") {
                        that.Model.setValue({id: 'fdeptid_ed', value: fdeptid, row: e.row, tgChange: false});
                    }
                    break;
                case 'fdeptperfratio':
                    that.calculateStaffAmountAndRatio(e, null, 'fdeptperfratio');
                    break;
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            var renewalflag = false;
            switch (e.opcode.toLowerCase()) {
                //充值
                case 'recharge':
                    e.result = true;
                    that.topUp(e);
                    break;
                //取消
                case 'settlecancel':
                    e.result = true;
                    that.Model.close();
                    break;
                //确定
                case 'settleconfirm':
                    e.result = true;
                    debugger
                    var cp = that.formContext.cp;
                    if (cp) {
                        renewalflag = cp.frenewalflag;
                        if (cp.fsourceformid == "ydj_order") {
                            var famount = that.Model.getValue({id: 'famount'})
                            if (that.orderfamount) {

                                if (that.orderfamount < famount) {
                                    yiDialog.warn('对不起，你输入的金额大于了应退金额，请检查！');
                                    return;
                                }

                                var parentModel = that.Model.getParentModel();
                                if (parentModel) {
                                    var orderNo = parentModel.getValue({id: 'fbillno'});
                                    if (parentModel.uiForm.uiMeta.id != "ydj_order") {
                                        orderNo = cp.fsourcenumber;
                                    }
                                    var orderAmount = that.orderfamount;
                                    var param = {
                                        simpleData: {
                                            formId: 'coo_settledyn',
                                            orderNo: orderNo,
                                            orderAmount, orderAmount,
                                            amount: famount,
                                            domainType: 'dynamic'
                                        }
                                    };

                                    var isReturn = false;
                                    yiAjax.p('/bill/coo_settledyn?operationno=checkamount', param, function (r) {
                                        that.Model.unblockUI({id: '#page#'});
                                        var res = r.operationResult;
                                        if (!res.isSuccess) {
                                            isReturn = true;
                                        }
                                    }, null, null, null, {async: false});

                                    if (isReturn) return;
                                }
                            }

                        }
                    }

                    var staffOk = that.checkStaff();
                    var deptPerRatioOk = that.checkStaffRatioIsOneHundredPercent('fdeptperfratio');
                    if (staffOk && deptPerRatioOk) {
                        //克隆一份当前页面的数据包
                        var cloneData = that.Model.clone();

                        //获取源单合同model
                        var parentModel = that.Model.getParentModel();
                        var orderBillType = "";
                        var orderDate = "";
                        if (parentModel && parentModel.uiForm.uiMeta.id === "ydj_order") {
                            orderBillType = parentModel.getValue({id: 'fbilltype'}).id;
                            orderDate = $.trim(parentModel.getValue({id: 'forderdate'}));
                        }

                        var fpaymentway = $.trim(cloneData.fpaymentway.id);
                        var way = $.trim(cloneData.fway.id);
                        if (!way && !Consts.isdirectsale) {
                            yiDialog.warn('支付方式不能为空！');
                            return;
                        }
                   
                        if (!fpaymentway && Consts.isdirectsale) {
                            yiDialog.warn('收款方式不能为空！');
                            return;
                        }
                        var accounts = [];
                        var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
                        var enableMustInputBankId = storeParam ? storeParam.fenablemustinputbankid : true;

                        var frefundcusacount = storeParam ? storeParam.frefundcusacount : false;
                        var fsettletype = $.trim(that.Model.getValue({id: 'fsettletype'}));
                        var fsettlemaintype = $.trim(that.Model.getValue({id: 'fsettlemaintype'}));
                        //当启用参数退款时客户收款账号必录且退款且结算主体为客户时
                        if (frefundcusacount && fsettletype == "退款" && fsettlemaintype == "ydj_customer" && way != "payway_01") {
                            var fcusacount = $.trim(cloneData.fcusacount);
                            if (!fcusacount) {
                                yiDialog.warn('客户收款账号不能为空！');
                                return;
                            }
                        }

                        //处理对方银行账号信息
                        if (way === 'payway_06' ||
                            way === 'payway_07' ||
                            way === 'payway_08' ||
                            (way === 'payway_11' && enableMustInputBankId)) {
                            if (!$.trim(cloneData.fmybankid.id)) {
                                yiDialog.warn('请选择银行账号！');
                                return;
                            }
                            if (cloneData.fissyn) {
                                var synBankId = $.trim(cloneData.fsynbankid.id);
                                if (!synBankId) {
                                    yiDialog.warn('请选择对方银行！');
                                    return;
                                }
                                var banks = cloneData.synBankNum;
                                if (banks && banks.length > 0) {
                                    for (var i = 0; i < banks.length; i++) {
                                        if ($.trim(banks[i].accountId) === synBankId) {
                                            cloneData.fsynbankname = banks[i].bankName;
                                            cloneData.fsynbanknum = banks[i].bankNum;
                                            cloneData.fsynaccountname = banks[i].accountName;
                                            break;
                                        }
                                    }
                                }
                            }
                        } else if (way === 'payway_01') {
                            var accountId = that.Model.getSimpleValue({"id": "faccount"});
                            if (!accountId || $.trim(accountId) == "") {
                                yiDialog.warn('请选择账户！');
                                return;
                            }
                            if (accountId && that.accounts && that.accounts.length > 0) {
                                for (var i = 0; i < that.accounts.length; i++) {
                                    if (that.accounts[i].accountId === accountId) {
                                        accounts.push({
                                            accountId: that.accounts[i].accountId,
                                            accountName: that.accounts[i].accountName
                                        });
                                        break;
                                    }
                                }
                            }
                        }
                        var paymentdesc = $.trim(cloneData.paymentdesc.id);
                        var paynum = $("select[name='paymentdesc']").find('option').length;
                        if (!paymentdesc && paynum > 0 && !Consts.isdirectsale) {
                            yiDialog.warn('款项说明不能为空！');
                            return;
                        }
                        //焕新订单非必填
                        var refundwaydesc = $.trim(cloneData.frefundwaydesc.id);
                        var payrefundnum = $("select[name='frefundwaydesc']").find('option').length;
                        if (!refundwaydesc && payrefundnum > 0 && Consts.isdirectsale && !renewalflag) {
                            yiDialog.warn('退款说明不能为空！');
                            return;
                        }
                        //焕新订单非必填
                        var frefundway = $.trim(cloneData.frefundway.id);
                        var frefundwaynum = $("select[name='frefundway']").find('option').length;
                        if (!frefundway && frefundwaynum > 0 && Consts.isdirectsale && !renewalflag) {
                            yiDialog.warn('退款方式不能为空！');
                            return;
                        }
                        //焕新订单非必填
                        debugger

                        // 精确选择：在 renewal-info-head-refund 容器内的 fdescription 字段
                        var refundReasonField = $('.renewal-info-head-refund [name="fdescription"]');
                        // 检查退款原因区域是否显示
                        if ($('.renewal-info-head-refund').is(':visible') && refundReasonField.is(':visible')) {
                            var value = refundReasonField.val();

                            if (!value || value.trim().length === 0 && Consts.isdirectsale && !renewalflag) {
                                yiDialog.warn('退款原因不能为空！');
                                return;
                            }
                        }

                        // var fdescription = $.trim(cloneData.fdescription.id);
                        // var fdescriptionnum = $("textarea[name='fdescription']").val();
                        // // 去除首尾空格
                        // fdescriptionnum = fdescriptionnum ? fdescriptionnum.trim() : "";
                        // if (!fdescription && fdescriptionnum.length > 0 && Consts.isdirectsale && !renewalflag) {
                        //     yiDialog.warn('退款原因不能为空！');
                        //     return;
                        // }

                        if (that.formContext.cp.fsourceformid == "stk_sostockreturn") {
                            if (cloneData.fsettleamount > that.formContext.cp.fsettleamount) {
                                yiDialog.warn('对不起，当前结算金额已超出销售退货单的实退货款金额，请重新调整结算金额！');
                                return;
                            }
                        }

                        var fdutyentry = [];
                        if (that.formContext.cp.fsourceformid == "ydj_order") {
                            var rows = that.Model.getEntryData({id: that.dutyEntryId});
                            for (var i = 0; i < rows.length; i++) {
                                fdutyentry.push({
                                    Id: rows[i].id,
                                    IsMain: rows[i].fismain,
                                    DutyId: rows[i].fdutyid.id,
                                    DeptId: rows[i].fdeptid_ed.id,
                                    Ratio: rows[i].fratio,
                                    Amount: rows[i].famount_ed,
                                    Description: rows[i].fdescription_ed,
                                    DeptPerfRatio: 100
                                });
                            }
                        }
                        debugger
                        var datafway=cloneData.fway.id;
                        if (Consts.isdirectsale){
                            //直营取收款方式
                            datafway=cloneData.fpaymentway.id;
                        }

                        that.Model.invokeFormOperation({
                            id: 'tbNewSettle',
                            opcode: 'NewSettle',
                            selectedRows: [{PKValue: cloneData.fsourceid}],
                            param: {
                                formId: cloneData.fsourceformid,
                                accounts: JSON.stringify(accounts),
                                fsettletype: cloneData.fsettletype,
                                fsettleamount: cloneData.fsettleamount,
                                paymentdesc: paymentdesc,
                                fwithin: cloneData.fwithin,
                                fsourceformid: cloneData.fsourceformid,
                                fsourceid: cloneData.fsourceid,
                                fsourcenumber: cloneData.fsourcenumber,
                                fsourcetranid: cloneData.fsourcetranid,
                                fsettlemaintype: cloneData.fsettlemaintype,
                                fsettlemainid: cloneData.fsettlemainid,
                                fpurpose: cloneData.fpurpose,
                                fdirection: cloneData.fdirection,
                                fbizdirection: cloneData.fbizdirection,
                                fcontactunittype: cloneData.fcontactunittype.id,
                                fcontactunitid: cloneData.fcontactunitid.id,
                                fimage: cloneData.fimage.id,
                                fway: datafway,
                                frefundway: cloneData.frefundway.id,
                                fbankcard: cloneData.fbankcard,
                                famount: cloneData.famount,
                                fdate: cloneData.fdate,
                                fdescription: cloneData.fdescription,
                                fbankcard: cloneData.fbankcard,
                                fmybankid: cloneData.fmybankid.id,
                                fsynbankid: cloneData.fsynbankid.id,
                                fsynbankname: cloneData.fsynbankname,
                                fsynbanknum: cloneData.fsynbanknum,
                                fsynaccountname: cloneData.fsynaccountname,
                                fdeptid: cloneData.fdeptid.id,
                                fstaffid: cloneData.fstaffid.id,
                                fmarketsettleno: cloneData.fmarketsettleno,
                                disableTransaction: true,
                                fotherpartyaccount: cloneData.fotherpartyaccount,
                                fcusacount: cloneData.fcusacount,
                                fdutyentry: JSON.stringify(fdutyentry),
                                freceiptno: cloneData.freceiptno,
                                forderbilltype: orderBillType,
                                forderdate: orderDate,
                                forderno: cp.forderid,
                                frefundwaydesc: refundwaydesc,
                            }
                        });
                    }
                    break;
            }
        };

        //充值
        _child.prototype.topUp = function (e) {
            var that = this;
            var settleMainType = $.trim(that.Model.getValue({id: 'fsettlemaintype'}));
            var settleMainId = $.trim(that.Model.getValue({id: 'fsettlemainid'}));
            if (settleMainId) {
                that.Model.showForm({
                    formId: 'coo_inpourdialog',
                    param: {openStyle: Consts.openStyle.modal},
                    cp: {
                        fusagetype: {
                            id: e.param.accountId,
                            fnumber: e.param.accountId,
                            fname: ''
                        },
                        pkid: settleMainId,
                        formId: settleMainType,
                        callback: function (result) {

                        }
                    }
                });
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'getagentinfo':
                    if (srvData && srvData.fmanagemodel) {
                        //经销商经营模式
                        that.fmanagemodel = srvData.fmanagemodel;
                    }
                    that.renewalHeadquart();
                    break;
                case 'newsettle':
                    if (isSuccess) {
                        //设置对话框的返回数据
                        that.Model.setReturnData({isSuccess: true});
                        //关闭对话框
                        that.Model.close();
                    }
                    break;
                case 'loadstoresysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var storesysparam = {};
                        for (var data in srvData) {
                            storesysparam[data] = srvData[data];
                        }
                        localStorage.setItem("storesysparam", JSON.stringify(storesysparam));
                    }
                    break;
                case 'checkinvoicenumber':
                    if (isSuccess && srvData) {
                        that.confirming = false;
                        yiDialog.c("【收款小票号】录入重复，您确定继续吗？", function () {
                            if (that.confirming) return;
                            that.confirming = true;

                            that.checkInvoiceNumberPass();
                        }, null, '温馨提示');
                    } else {
                        that.checkInvoiceNumberPass();
                    }
                    break;
            }
        };

        //根据员工带出其主岗位对应的部门(通过mdl配置带出的部门不正确，需要是员工明细勾选的主岗位)
        _child.prototype.GetDeptByStaff = function (fid) {
            var that = this;
            var fdeptid = "";
            var param = {
                simpleData: {
                    formId: 'coo_incomedisburse',
                    fid: fid,
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/coo_incomedisburse?operationno=getdeptbyfid', param, function (r) {
                that.Model.unblockUI({id: '#page#'});
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (!res.isSuccess) {
                    fdeptid = "";
                } else {
                    fdeptid = srvData["fdeptid"];
                }
            }, null, null, null, {async: false});
            return fdeptid;
        };

        //根据客户带出客户银行账号
        _child.prototype.GetCusacount = function (fid) {
            var that = this;
            var fbanknumber = "";
            var param = {
                simpleData: {fid: fid}
            };
            yiAjax.p('/dynamic/coo_chargedialog?operationno=getcusaccount', param, function (r) {
                that.Model.unblockUI({id: '#page#'});
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (!res.isSuccess) {
                    fbanknumber = "";
                } else {
                    fbanknumber = srvData["fbanknumber"];
                }
            }, null, null, null, {async: false});
            return fbanknumber;
        };

        //联合开单数据处理主岗位数据
        _child.prototype.DealDutyEntry = function (isadd, e) {
            var that = this;
            var dutyEntry = that.Model.getEntryData({id: that.dutyEntryId});
            if (isadd) {
                // 固定首行数据
                that.Model.addRow({
                    id: that.dutyEntryId, data: {
                        fismain: true,
                        fdutyid: that.Model.getValue({id: 'fstaffid'}),
                        fdeptid_ed: that.Model.getValue({id: 'fdeptid'}),
                        fratio: 100,
                        famount_ed: 0,
                        fdeptperfratio: 100
                    }
                });
                for (var i = 0; i < dutyEntry.length; i++) {
                    if (dutyEntry[i].fismain) {
                        var row = dutyEntry[i].id;
                        that.Model.setEnable({id: 'fdutyid', row: row, value: false});
                        that.Model.setEnable({id: 'fdeptid_ed', row: row, value: false});
                        break;
                    }
                }
            } else {
                //修改首行数据
                for (var i = 0; i < dutyEntry.length; i++) {
                    if (dutyEntry[i].fismain) {
                        var row = dutyEntry[i].id;
                        if (e && e.id == "fstaffid") {
                            that.Model.setValue({id: 'fdutyid', row: row, value: e.value.id, tgChange: true});
                        }
                        if (e && e.id == "fdeptid") {
                            that.Model.setValue({id: 'fdeptid_ed', row: row, value: e.value.id});
                        }
                        break;
                    }
                }
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.dutyEntryId:
                    var isMain = that.Model.getValue({id: 'fismain', row: e.row});
                    if (isMain) {
                        e.result = true;
                        yiDialog.mt({msg: '主要销售员不允许删除！', skinseq: 2});
                        return;
                    }
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.dutyEntryId:
                    that.calculateStaff("delete");
                    that.calculateStaffAmountAndRatio("delete", null, 'fdeptperfratio');
                    break;
            }
        }

        //计算销售员金额比例行联动
        _child.prototype.calculateStaff = function (e) {
            var that = this,
                data = that.Model.getEntryData({id: that.dutyEntryId}),
                sumAmount = yiMath.toNumber(that.Model.getValue({id: 'famount'}));
            if (!data || data.length <= 0) {
                return;
            }

            //只有一个导购员时，自动设置为订单总额
            if (data.length === 1) {
                data[0].famount_ed = sumAmount;
                data[0].fratio = 100;
                that.Model.refreshEntry({id: that.dutyEntryId});
                return;
            }
            //有两个导购员时，修改其中一个导购员的金额或比例后，自动计算另外一个导购员的金额和比例
            if (e && data.length === 2) {
                if (e == "delete") {
                    //删除行小于等于2个人的时候需要做计算给到第一个人，即100%减去第2个人给到第1个人
                    for (var i = 0; i < data.length; i++) {
                        if (i == 0) {
                            data[i].fratio = 100 - data[1].fratio;
                        }
                        data[i].famount_ed = yiMath.toDecimal(sumAmount * data[i].fratio / 100, 2);
                    }
                } else {
                    for (var i = 0; i < data.length; i++) {
                        //金额字段可编辑，自动反算比例
                        if (e.id == "famount_ed") {
                            if (sumAmount <= 0) continue;
                            e.value = e.value > sumAmount || e.value < 0 ? sumAmount : e.value;
                            if (data[i].id === e.row) {
                                data[i].famount_ed = e.value;
                            } else {
                                data[i].famount_ed = sumAmount - e.value;
                            }
                            data[i].fratio = yiMath.toDecimal(data[i].famount_ed / sumAmount * 100, 2);
                        }
                        //比例字段可编辑，并可自动反算金额
                        if (e.id == "fratio") {
                            e.value = e.value > 100 || e.value < 0 ? 100 : e.value;
                            if (data[i].id === e.row) {
                                data[i].fratio = e.value;
                            } else {
                                data[i].fratio = 100 - e.value;
                            }
                            data[i].famount_ed = yiMath.toDecimal(sumAmount * data[i].fratio / 100, 2);
                        }
                    }
                }
                that.Model.refreshEntry({id: that.dutyEntryId});
                return;
            }

            var useAmt = sumAmount;
            var useRatio = 100.00;
            //单行金额或者比例计算
            for (var i = 0, l = data.length; i < l; i++) {
                //金额字段可编辑，自动反算比例
                if (e && e.id == "famount_ed") {
                    if (sumAmount <= 0) continue;
                    //行所占百分比 = 金额/订单总额 * 100
                    data[i].famount_ed = data[i].famount_ed > sumAmount || data[i].famount_ed < 0 ? 0 : data[i].famount_ed;
                    data[i].fratio = yiMath.toDecimal(data[i].famount_ed / sumAmount * 100, 2);
                } else {
                    var ratio = yiMath.toNumber(data[i].fratio);
                    //金额 = 订单总额 * 行所占百分比
                    data[i].fratio = ratio > 100 || ratio < 0 ? 0 : data[i].fratio;
                    data[i].famount_ed = yiMath.toDecimal(sumAmount * data[i].fratio / 100, 2);
                }
                useAmt -= yiMath.toDecimal(data[i].famount_ed, 2);
                useRatio -= yiMath.toDecimal(data[i].fratio, 2);
            }

            //处理比例/金额尾差分配给主销售员
            var mainrow = data.findIndex(item => item.fismain === true);
            if (useAmt > 0) data[mainrow].famount_ed = yiMath.toDecimal(yiMath.toNumber(data[mainrow].famount_ed) + yiMath.toNumber(useAmt), 2);
            if (useRatio > 0) data[mainrow].fratio = yiMath.toDecimal(yiMath.toNumber(data[mainrow].fratio) + yiMath.toNumber(useRatio), 2);

            that.Model.refreshEntry({id: that.dutyEntryId});
        };

        //检查销售员比例和金额是否填写正确
        _child.prototype.checkStaff = function () {
            var that = this;

            var fsourceformid = that.Model.getValue({id: 'fsourceformid'})
            if (fsourceformid == "ydj_order") {
                var ratioSum = 0;
                var amountSum = 0;
                var data = that.Model.getEntryData({id: that.dutyEntryId});
                if (data && data.length > 0) {
                    for (var i = 0, l = data.length; i < l; i++) {
                        ratioSum = yiMath.toNumber(yiMath.toDecimal(ratioSum, 2));
                        ratioSum += yiMath.toNumber(data[i].fratio);

                        amountSum += yiMath.toNumber(yiMath.toDecimal(data[i].famount_ed, 2));
                    }
                }
                if (ratioSum != 100) {
                    yiDialog.warn('销售员分配比例总和必须等于100%');
                    return false;
                }
                var sumAmount = yiMath.toNumber(that.Model.getValue({id: 'famount'}));
                if (amountSum != sumAmount) {
                    yiDialog.warn('销售员分配金额总和必须等于收款金额！');
                    return false;
                }
            }
            return true;
        };

        //检查收款小票号跟金额
        _child.prototype.checkInvoiceNumber = function () {
            var that = this;
            var receiptno = that.Model.getValue({id: 'freceiptno'});
            if (receiptno) {
                var money = that.Model.getValue({id: 'famount'});
                that.Model.invokeFormOperation({
                    id: 'checkinvoicenumber',
                    opcode: 'checkinvoicenumber',
                    param: {
                        'formId': 'coo_incomedisburse',
                        'freceiptno': receiptno,
                        'fmoney': money
                    }
                });
            }
        };

        //检查收款小票号跟金额通过
        _child.prototype.checkInvoiceNumberPass = function () {
            var that = this;
            var cloneData = that.Model.clone();
            var paymentdesc = $.trim(cloneData.paymentdesc.id);

            var accounts = [];
            var way = $.trim(cloneData.fway.id);
            if (way === 'payway_01') {
                var accountId = that.Model.getSimpleValue({"id": "faccount"});
                if (accountId && that.accounts && that.accounts.length > 0) {
                    for (var i = 0; i < that.accounts.length; i++) {
                        if (that.accounts[i].accountId === accountId) {
                            accounts.push({
                                accountId: that.accounts[i].accountId,
                                accountName: that.accounts[i].accountName
                            });
                            break;
                        }
                    }
                }
            }

            var fdutyentry = [];
            if (that.formContext.cp.fsourceformid == "ydj_order") {
                var rows = that.Model.getEntryData({id: that.dutyEntryId});
                for (var i = 0; i < rows.length; i++) {
                    fdutyentry.push({
                        Id: rows[i].id,
                        IsMain: rows[i].fismain,
                        DutyId: rows[i].fdutyid.id,
                        DeptId: rows[i].fdeptid_ed.id,
                        Ratio: rows[i].fratio,
                        Amount: rows[i].famount_ed,
                        Description: rows[i].fdescription_ed,
                        DeptPerfRatio: rows[i].fdeptperfratio
                    });
                }
            }
            that.Model.invokeFormOperation({
                id: 'tbNewSettle',
                opcode: 'NewSettle',
                selectedRows: [{PKValue: cloneData.fsourceid}],
                param: {
                    formId: cloneData.fsourceformid,

                    accounts: JSON.stringify(accounts),
                    fsettletype: cloneData.fsettletype,
                    fsettleamount: cloneData.fsettleamount,
                    paymentdesc: paymentdesc,
                    fwithin: cloneData.fwithin,
                    fsourceformid: cloneData.fsourceformid,
                    fsourceid: cloneData.fsourceid,
                    fsourcenumber: cloneData.fsourcenumber,
                    fsourcetranid: cloneData.fsourcetranid,
                    fsettlemaintype: cloneData.fsettlemaintype,
                    fsettlemainid: cloneData.fsettlemainid,
                    fpurpose: cloneData.fpurpose,
                    fdirection: cloneData.fdirection,
                    fbizdirection: cloneData.fbizdirection,
                    fcontactunittype: cloneData.fcontactunittype.id,
                    fcontactunitid: cloneData.fcontactunitid.id,
                    fimage: cloneData.fimage.id,
                    fway: cloneData.fway.id,
                    fbankcard: cloneData.fbankcard,
                    famount: cloneData.famount,
                    fdate: cloneData.fdate,
                    fdescription: cloneData.fdescription,
                    fbankcard: cloneData.fbankcard,
                    fmybankid: cloneData.fmybankid.id,
                    fsynbankid: cloneData.fsynbankid.id,
                    fsynbankname: cloneData.fsynbankname,
                    fsynbanknum: cloneData.fsynbanknum,
                    fsynaccountname: cloneData.fsynaccountname,
                    fdeptid: cloneData.fdeptid.id,
                    fstaffid: cloneData.fstaffid.id,
                    fmarketsettleno: cloneData.fmarketsettleno,
                    disableTransaction: true,
                    fotherpartyaccount: cloneData.fotherpartyaccount,
                    fcusacount: cloneData.fcusacount,
                    fdutyentry: JSON.stringify(fdutyentry),
                    freceiptno: cloneData.freceiptno
                }
            });
        };

        _child.prototype.calculateStaffAmountAndRatio = function (e, amount, ratio) {
            var that = this;
            data = that.Model.getEntryData({id: that.dutyEntryId});
            sumAmount = yiMath.toNumber(that.Model.getValue({id: 'fmoney'}));
            if (!data || data.length <= 0) {
                return;
            }

            //只有一个导购员时，自动设置为订单总额
            if (data.length === 1) {
                //data[0][amount] = sumAmount;
                data[0][ratio] = 100;

                that.Model.refreshEntry({id: that.dutyEntryId});
                return;
            }
            //有两个导购员时，修改其中一个导购员的金额或比例后，自动计算另外一个导购员的金额和比例
            if (e && data.length === 2) {
                if (e == "delete") {
                    //删除行小于等于2个人的时候需要做计算给到第一个人，即100%减去第2个人给到第1个人
                    for (var i = 0; i < data.length; i++) {
                        if (i == 0) {
                            data[i][ratio] = 100 - data[1][ratio];
                        }
                        //data[i][amount] = yiMath.toDecimal(sumAmount * data[i][ratio] / 100, 2);
                    }
                } else {
                    for (var i = 0; i < data.length; i++) {
                        //金额字段可编辑，自动反算比例
                        if (e.id === amount) {
                            if (sumAmount <= 0) continue;
                            e.value = e.value > sumAmount || e.value < 0 ? sumAmount : e.value;
                            if (data[i].id === e.row) {
                                data[i][amount] = e.value;
                            } else {
                                data[i][amount] = sumAmount - e.value;
                            }
                            data[i][ratio] = yiMath.toDecimal(data[i][amount] / sumAmount * 100, 2);
                        }
                        //比例字段可编辑，并可自动反算金额
                        if (e.id === ratio) {
                            e.value = e.value > 100 || e.value < 0 ? 100 : e.value;
                            if (data[i].id === e.row) {
                                data[i][ratio] = e.value;
                            } else {
                                data[i][ratio] = 100 - e.value;
                            }
                            //data[i][amount] = yiMath.toDecimal(sumAmount * data[i][ratio] / 100, 2);
                        }
                    }
                }

                that.Model.refreshEntry({id: that.dutyEntryId});
                return;
            }

            var useAmt = sumAmount;
            var useRatio = 100.00;
            //单行金额或者比例计算
            for (var i = 0, l = data.length; i < l; i++) {
                //金额字段可编辑，自动反算比例
                if (e && e.id === amount) {
                    if (sumAmount <= 0) continue;
                    //行所占百分比 = 金额/订单总额 * 100
                    //data[i][amount] = data[i][amount] > sumAmount || data[i][amount] < 0 ? 0 : data[i][amount];
                    data[i][ratio] = yiMath.toDecimal(data[i][amount] / sumAmount * 100, 2);
                } else {
                    var ratio = yiMath.toNumber(data[i].fratio);
                    //金额 = 订单总额 * 行所占百分比
                    data[i][ratio] = ratio > 100 || ratio < 0 ? 0 : data[i][ratio];
                    //data[i][amount] = yiMath.toDecimal(sumAmount * data[i][ratio] / 100, 2);
                }
                //useAmt -= yiMath.toDecimal(data[i][amount], 2);
                useRatio -= yiMath.toDecimal(data[i][ratio], 2);
            }

            //处理比例/金额尾差分配给主销售员
            var mainrow = data.findIndex(item => item.fismain === true);
            //if (useAmt > 0) data[mainrow][amount] = yiMath.toDecimal(yiMath.toNumber(data[mainrow][amount]) + yiMath.toNumber(useAmt), 2);
            if (useRatio > 0) data[mainrow][ratio] = yiMath.toDecimal(yiMath.toNumber(data[mainrow][ratio]) + yiMath.toNumber(useRatio), 2);

            that.Model.refreshEntry({id: that.dutyEntryId});
        }
        //检查销售员比例和金额是否填写正确
        _child.prototype.checkStaffRatioIsOneHundredPercent = function (ratio) {
            var that = this;
            var ratioSum = 0;
            //直营模式下面才进行下面的判断
            if (!Consts.isdirectsale) {
                return true;
            }
            var data = that.Model.getEntryData({id: that.dutyEntryId});
            if (data && data.length > 0) {
                for (var i = 0, l = data.length; i < l; i++) {
                    ratioSum = yiMath.toNumber(yiMath.toDecimal(ratioSum, 2));
                    ratioSum += yiMath.toNumber(data[i][ratio]);

                }
            }
            if (ratioSum != 100) {
                var errormsg = '';
                switch (ratio.toLocaleLowerCase()) {
                    //部门业绩比例
                    case 'fdeptperfratio':
                        errormsg = '部门业绩比例总和必须等于100%！';
                        break;
                }
                yiDialog.warn(errormsg);
                return false;
            }
            return true;
        };
        //直营的时候需要把联合开单的比例显示，经销商隐藏隐藏
        _child.prototype.hideOrShowDutyEntryField = function (dutyEntryId, fieldIdArray) {
            var that = this;
            var dutyEntry = that.Model.getEntryData({id: dutyEntryId});
            var fieldIdList = fieldIdArray.split(',');
            if (Consts.isdirectsale) {
                if (dutyEntry && dutyEntry.length > 0) {
                    for (var i = 0; i < dutyEntry.length; i++) {
                        for (var j = 0; j < fieldIdList.length; j++) {
                            that.Model.setVisible({id: fieldIdList[i], value: true});
                        }
                    }
                }
            } else {
                if (dutyEntry && dutyEntry.length > 0) {
                    for (var i = 0; i < dutyEntry.length; i++) {
                        for (var j = 0; j < fieldIdList.length; j++) {
                            that.Model.setVisible({id: fieldIdList[i], value: false});
                        }
                    }
                }
            }
        }

        return _child;
    })(BasePlugIn);
    window.coo_settledyn = window.coo_settledyn || coo_settledyn;
})();