/*
    采购入库单插件
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/stk/stk_postockin.js
 */
; (function () {
    var stk_postockin = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.filesavemsg = [];//全局存储信息
            that.result = [];
            that.tempId = [];
            that.succArr = [];
            that.nowRow = '';//当前被双击的行
            that.suentry = [];
            that.prodRecord = {};//商品行记录
            that.remindnumbers = "";
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';//商品信息

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.fentity:
                    break;
            }
        };

        //页面视图渲染时触发
        _child.prototype.onPageViewRendering = function (e) {
            var that = this;
            var controlOptions = ["new", "modify", "view", "push"];

            if (that.formContext && that.formContext.status && controlOptions.indexOf(that.formContext.status) >= 0) {
                if (that.uiForm && that.uiForm.uiMeta) {
                    yiAjax.p('/dynamic/stk_postockin?operationno=QueryFieldsPermission', null, function (r) {
                        var srvData = r.operationResult.srvData;
                        if (srvData) {
                            var uiMeta = that.uiForm.uiMeta;
                            if (uiMeta.fsupplierorderno) {
                                uiMeta.fsupplierorderno.allowShowOrHide = srvData.isPermissionAgent;
                            }
                            if (uiMeta.fprice) {
                                uiMeta.fprice.editable = srvData.isVisibleAmountRole;
                            }
                            if (uiMeta.famount) {
                                uiMeta.famount.editable = srvData.isVisibleAmountRole;
                            }
                        }
                    }, null, null, null, { async: false });
                }
            }
        };

        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            that.SetImageInfoVisible();

            //创建状态
            if (that.formContext.status === "new") {
                that.dealfamount();
            }
            that.setDropShipMentMenuVisible();
            that.setDropShipMentVisible();
        };


        //设置图片隐藏属性
        _child.prototype.SetImageInfoVisible = function (e) {
            var that = this;
            var fimage = that.Model.getValue({ id: 'fdeliveryvoucher' });
            var hasimage = false;
            if (fimage && fimage.id.length > 0) hasimage = true;
            //如果有图片，则默认展开
            setTimeout(function () {
                // debugger;
                that.Model.setAttr({ id: '.s_postockin_tools', random: 'class', value: hasimage ? 's_postockin_tools collapse' : 's_postockin_tools expand' });
                that.Model.setAttr({ id: '.s_postockin_portlet', random: 'style', value: hasimage ? 'display:block' : 'display:none' });
            }, 10);
        };

        _child.prototype.entrycopy = function () {
            debugger;
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行关联复制行！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('关联复制行不支持多选！');
                return;
            };
            if (ds) {
                var newentry = $.extend(true, [], ds);
                var newid = yiCommon.uuid(18);
                newentry[0].data.id = newid;
                newentry[0].pkid = newid;
                newentry[0].data.fiscopy = 1;
                var row = that.Model.addRow({ id: that.fentity, pid: ds[0].pkid, data: newentry[0].data });
                that.Model.setEnable({ id: "fmaterialid", row: row, value: false });
                var fields = that.Model.uiForm.getAllFields();
                //仓库、数量不锁定，其它都锁定
                var noLockFields = ["fstorelocationid", "fentrynote", "fbizqty", "fbizplanqty"];
                for (var i = 0; i < fields.length; i++) {
                    if (that.uiForm.getField(fields[i]).entityKey != that.fentity) {
                        continue;
                    }
                    if (noLockFields.indexOf(fields[i]) > -1) {
                        continue;
                    }
                    that.Model.setEnable({ id: fields[i], row: row, value: false });
                }
            }
        };

        //初始化商品明细表的经销价插件
        _child.prototype.onBillInitProduct = function (param, params) {

            var that = this;
            var productData = [];

            if (param == 'init') {//初始化，


            } else if (param == 'change') {//改变某一行

                var rowData = that.Model.getEntryRowData({ id: that.fentity, row: params.attrinfo.row });

                var reAttrinofEntry = [];//按照接口，重新组合
                var tempAttr = [];
                if (rowData.fattrinfo) {
                    tempAttr = rowData.fattrinfo.fentity;
                }

                for (var n = 0, m = tempAttr.length; n < m; n++) {
                    reAttrinofEntry.push({
                        valueId: tempAttr[n].fvalueid,
                        auxPropId: tempAttr[n].fauxpropid.id
                    })
                }

                productData.push({
                    clientId: rowData.id,
                    productId: rowData.fmaterialid.id,
                    bizDate: that.Model.uiData.fdate,
                    length: rowData.flength,
                    width: rowData.fwidth,
                    thick: rowData.fthick,
                    attrInfo: {
                        id: '',
                        entities: reAttrinofEntry
                    }
                });


            }

            productData = JSON.stringify(productData);

            that.Model.invokeFormOperation({
                id: param,

                opcode: 'getprices',
                //option: cvtParams,
                param: {
                    productInfos: productData,
                    formId: 'ydj_price',
                    domainType: 'dynamic'
                }
            });
        }

        //报价明细 辅助属性价格查询按钮点击事件
        _child.prototype.onPriceSerch = function (e) {
            var that = this;
            var flag = true;
            that.alertModel = e;
            var productData = [];
            var reAttrinofEntry = [];//按照接口，重新组合 
            var fentry = that.alertModel.Model.uiData.fentity;
            for (var n = 0, m = fentry.length; n < m; n++) {
                var lm = fentry[n];
                if (lm.fisselect) {//被选中的辅助属性行

                    if (!lm.fvalueid) {//辅助属性行需要填满信息才能查询
                        flag = false;
                    }
                    reAttrinofEntry.push({
                        valueId: lm.fvalueid,
                        auxPropId: lm.fauxpropid.id
                    })
                }

            }

            productData.push({
                clientId: '',
                productId: that.alertModel.Model.uiData.fmaterialid.id,
                bizDate: that.Model.getValue({ id: 'fdate' }),//订单日期
                length: that.alertModel.Model.uiData.flength,
                width: that.alertModel.Model.uiData.fwidth,
                thick: that.alertModel.Model.uiData.fthick,
                attrInfo: {
                    id: '',
                    entities: reAttrinofEntry
                }
            });

            productData = JSON.stringify(productData);
            if (flag) {
                that.Model.invokeFormOperation({
                    id: 'onPriceSerch',

                    opcode: 'getprices',
                    //option: cvtParams,
                    param: {
                        productInfos: productData,
                        formId: 'ydj_price',
                        domainType: 'dynamic'
                    }
                });
            } else {
                yiDialog.mt({ msg: '辅助属性信息不全，无法查询价格。', skinseq: 2 });
            }


        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    //that.onBillInitProduct('change',{attrinfo:e});
                    break;
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    var isMain = that.Model.getValue({ id: 'fpublishstatus' });
                    //采购订单，发布状态  fpublishstatus=='publish_status_02' 的时候，全部锁住
                    if (isMain && isMain.id == 'publish_status_02') {
                        e.result = true;
                        yiDialog.mt({ msg: '已经建立协同关系，不允许删除商品明细！', skinseq: 2 });
                    }
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    //重新计算表头字段值

                    break;
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var isMain = that.Model.getValue({ id: 'fpublishstatus' });
            var isCopy = that.Model.getValue({ id: 'fiscopy', row: e.row });
            ////复制行只能 编辑仓库、仓位、备注、数量类的字段
            //if (isCopy && !(e.id.toLowerCase() == "fstorehouseid"
            //    || e.id.toLowerCase() == "fstorelocationid"
            //    || e.id.toLowerCase() == "fentrynote"
            //    || e.id.toLowerCase() == "fbizqty"
            //    || e.id.toLowerCase() == "fbizplanqty"
            //)
            //) {
            //    e.result.enabled = false;
            //    return;
            //}

            switch (e.id.toLowerCase()) {

                case 'fattrinfo':
                    //商品为空时，不允许编辑
                    productId = $.trim(that.Model.getSimpleValue({ id: 'fmaterialid', row: e.row }));
                    if (!productId) {
                        e.result.enabled = false;
                        return;
                    }
                    break;
            }
        };


        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                //仓库：按库存参数中控制可选仓库范围
                case 'fstorehouseid':
                    var deptid = that.Model.getSimpleValue({ id: 'fstockdeptid' });
                    var srcPara = {
                        formid: 'stk_postockin',
                        deptid: deptid,
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
                //商品基础资料
                case 'fmaterialid':
                    var fsupplierid = that.Model.getValue({ "id": "fsupplierid" })
                    var srcPara = {
                        supplierid: fsupplierid ? fsupplierid.id : "",
                        supplierNo: fsupplierid ? fsupplierid.id : "",
                        supplierName: fsupplierid ? fsupplierid.id : "",
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
            }
        };


        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {

                //数量单价的变化，影响金额
                case 'fqty':
                case 'fprice':
                    that.culNum({ name: e.id, rowId: e.row, value: e.value });
                    break;
                case 'fmaterialid':
                    that.Model.setValue({ id: 'fattrinfo', row: e.row, value: { fentity: [] }, ctx: { tgFatchPrice: false } });
                    that.Model.setValue({ id: 'fattrinfo_e', row: e.row, value: { fentity: [] }, ctx: { tgFatchPrice: false } });
                    that.SetDefaultStockInfo(true, e.row)
                    break;
                case 'fstockdeptid':
                    that.SetDefaultStockInfo(false, e.row)
                    break;
                case 'fsupplierid':
                    that.clearEntry();
                    break;
                case 'fdescription':
                    if (e.tgChange == false) {
                        return;
                    }
                    that.SaveEditInfo(e.id.toLowerCase(), e.value);
                    break;
            }
        };

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == that.fentity) {
                switch (e.btnid.toLowerCase()) {
                    case 'g_record': //批录按钮 
                        //仓位禁止批录，需求#35670 
                        if (e.fieldId.toLowerCase() === 'fstorehouseid') {
                            e.copyFields = ["fstockstatus"];
                            e.clearFields = ["fstorelocationid"];
                        }
                        break;
                }
            }
        };

        ///审核状态下的备注字段修改直接保存
        _child.prototype.SaveEditInfo = function (fileName, value) {
            var that = this;
            var fileValue = value;
            if (fileName == 'finstallerid') {
                fileValue = value.id;
            }
            /*if(fileName == 'fdescription'){
                fileValue = value.id;
                
            }*/
            var status = that.Model.getSimpleValue({ id: 'fstatus' });
            if (status == 'E' || status == 'D') {
                that.Model.invokeFormOperation({
                    id: 'tbSave',
                    opcode: 'saveeditinfo',
                    param: {
                        pkid: this.Model.pkid,
                        fileName: fileName,
                        fileValue: fileValue,
                    }
                });
            }
            that.Model.setValue({ id: fileName, tgChange: false });
        }

        ///获取默认的仓库
        _child.prototype.SetDefaultStockInfo = function (isMatChange, rowIndex) {
            debugger
            var that = this;
            var deptid = that.Model.getSimpleValue({ id: 'fstockdeptid' });
            if (!deptid) {
                //没有设置部门，不需要请求获取数据
                return;
            }
            if (isMatChange) {
                var fstorehouseid = that.Model.getSimpleValue({ id: 'fstorehouseid', row: rowIndex });
                if (fstorehouseid) {
                    //修改物料时，如果已经有仓库，不需要再设置默认的
                    return;
                }
            }

            yiAjax.p('/bill/ydj_storehouse?operationno=getdefaultstockinfo&srcformid=stk_postockin&deptid=' + deptid, null,
                function (r) {
                    var data = r.operationResult;
                    if (data.isSuccess) {
                        //库存参数中启用了部门仓库控制
                        var stockInfo = data.srvData;
                        if (stockInfo) {
                            //设置默认仓库
                            if (isMatChange) {
                                that.Model.setValue({ id: 'fstorehouseid', row: rowIndex, value: stockInfo.id });
                            }
                            else {
                                var ds = that.Model.getEntryData({ id: 'fentity' });
                                for (var i = 0, j = ds.length; i < j; i++) {
                                    that.Model.setValue({ id: 'fstorehouseid', row: ds[i].id, value: stockInfo.id });
                                }
                            }
                        }
                        else {
                            //清空仓库
                            if (isMatChange) {
                                that.Model.setValue({ id: 'fstorehouseid', row: rowIndex, value: "" });
                            }
                            else {
                                var ds = that.Model.getEntryData({ id: 'fentity' });
                                for (var i = 0, j = ds.length; i < j; i++) {
                                    that.Model.setValue({ id: 'fstorehouseid', row: ds[i].id, value: '' });
                                }
                            }
                        }
                    } else {

                    }
                }, null, null, null, { async: false }
            );

        }






        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case "querylock":
                    if (!isSuccess || !srvData) {
                        return false;
                    }
                    var enable = true;
                    if (srvData[0].flock == '-1') {
                        //全部锁定
                        enable = false;

                    } else {
                        if ($.trim(that.Model.pkid)) {
                            //修改锁定
                            if (srvData[0].flock == '2') {
                                enable = false;
                            }
                        } else {
                            //新增锁定
                            if (srvData[0].flock == '1') {
                                enable = false;
                            }
                        }
                    }
                    that.Model.setEnable({ id: srvData[0].ffieldid, value: enable });
                    break;
                //case 'isexsitbarcode':
                //    //if (!isSuccess) {
                //    //    yiDialog.warn("当前商品未查询到对应条码信息!");
                //    //    return false;
                //    //}
                //    //弹出《条码主档》列表界面
                //    var filterString = "fmainorgid = '{0}' and fid in ({1})"
                //        .format(Consts.loginCompany.id, srvData.split(','));
                //    that.Model.showForm({
                //        formId: 'bcm_barcodemaster',
                //        domainType: Consts.domainType.list,
                //        param: {
                //            openStyle: Consts.openStyle.modal,
                //            filterstring: filterString
                //        }
                //    });
                //    break;
            }
        };

        _child.prototype.culNum = function (opt) {
            var that = this;
            //行对象
            var row = that.Model.getEntryRowData({ id: that.fentity, row: opt.rowId });

            if (!row) return;

            if (row.fbizorderqty != row.fbizqty) {
                //如果不全部出库，则出库单价和之前不等时，设置为之前的成交单价
                var parm = that.prodRecord[opt.rowId];
                if (parm && parm.fprice !== row.fprice) {
                    that.Model.setValue({ id: 'fprice', row: opt.rowId, value: parm.fprice });
                }
            }
            else {
                that.dealfamount(row);
            }
        };

        // 处理下推金额
        _child.prototype.dealfamount = function (row) {
            var that = this;

            var poorderentryidarr = [];
            var ds = that.Model.getEntryData({ id: that.fentity });
            if (!row) {
                ds.forEach(e => {
                    if (e.fpoorderentryid != "") {
                        poorderentryidarr.push(e.fpoorderentryid);
                    }
                });
            }
            else {
                poorderentryidarr.push(row.fpoorderentryid);
            }

            if (poorderentryidarr.join(',') != "") {
                var param = {
                    simpleData: {
                        formId: 'stk_postockin',
                        fpoorderentryid: poorderentryidarr.join(','),
                        pkId: that.Model.pkid,
                        domainType: 'dynamic'
                    }
                };
                yiAjax.p('/bill/stk_postockin?operationno=queryprice', param, function (r) {
                    var res = r.operationResult;
                    if (res.isSuccess && res.srvData) {
                        ds.forEach(e => {
                            var product = res.srvData.find(c => c.poorderEntryid == e.fpoorderentryid);
                            //最后一次入库时处理差额
                            if (product && product.purQty == e.fqty + product.inQty) {
                                //如果 当前金额+当前商品其他已出库金额!=合同金额
                                var famount = yiMath.toDecimal(e.fqty * e.fprice, 2); //当前金额
                                var inAmount = yiMath.toNumber(product.inAmount);//其他已入库金额
                                var purAmount = yiMath.toNumber(product.purAmount);//采购金额
                                if (yiMath.toNumber(famount) + inAmount != purAmount) {
                                    var parm = {
                                        fprice: that.Model.getValue({ id: "fprice", row: e.id }),//明细行成交单价
                                    }
                                    that.prodRecord[e.id] = parm;//记录原有成交单价

                                    that.Model.setValue({ id: 'famount', row: e.id, value: purAmount - inAmount });
                                    that.Model.setValue({ id: 'fprice', row: e.id, value: (purAmount - inAmount) / e.fqty, tgChange: false });
                                }
                            }
                        });
                    }
                }, null, null, null, { async: false });
            }
        }

        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            debugger;
            switch (e.opcode.toLowerCase()) {
                case 'entrycopy':
                    e.result = true;
                    that.entrycopy();
                    break;
                case 'pull':
                    that.pull(e);
                    break;
                case 'rejectflow':
                    e.result = true;
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    if (checkorder && that.remindnumbers) {
                        yiDialog.d({
                            id: 'remindnumbers',
                            type: 1,
                            resize: false,
                            maxmin: false,
                            title: '系统提示',
                            content: that.remindnumbers,
                            area: ['400px', '200px'],
                            btn: ['确定'],
                            yes: function (index, layero) {
                                layer.close(index);
                            }
                        });
                    } else {
                        var params = {
                            selectedRows: that.Model.getSelectRows(),
                            simpleData: {
                                pkid: that.Model.pkid
                            }
                        };

                        that.Model.blockUI({ id: '#page#' });
                        yiAjax.p('/bill/stk_postockin?operationno=CheckLinkReserveInfo', params, function (r) {
                            //隐藏遮罩层
                            that.Model.unblockUI({ id: '#page#' });
                            var res = r.operationResult.srvData;
                            if (res && res != '') {
                                yiDialog.c(res, function () {
                                    // 确定 
                                    that.Model.invokeFormOperation({
                                        id: 'tbUnAudit',
                                        opcode: 'rejectflow',
                                        param: {
                                            formId: 'stk_postockin',
                                        }
                                    });
                                }, function () {
                                    // 取消
                                }, '关联预留删除提示');

                            } else {
                                that.Model.invokeFormOperation({
                                    id: 'tbUnAudit',
                                    opcode: 'rejectflow',
                                    param: {
                                        formId: 'stk_postockin',
                                    }
                                });
                            }
                        }, null, null, null, { async: false });

                        //隐藏遮罩层
                        that.Model.unblockUI({ id: '#page#' });
                    }
                    break;
                case 'barcodequery':
                    that.querybarcode(e);
                    break;

                case 'save':
                case 'savesubmit':
                    e.result = true;
                    var param = e.param;
                    param.formId = "stk_postockin";
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: param
                    });
                    break;
                case 'saveaudit':
                    e.result = true;
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    if (checkorder && that.remindnumbers) {
                        yiDialog.d({
                            id: 'remindnumbers',
                            type: 1,
                            resize: false,
                            maxmin: false,
                            title: '系统提示',
                            content: that.remindnumbers,
                            area: ['400px', '200px'],
                            btn: ['确定'],
                            yes: function (index, layero) {
                                layer.close(index);
                            }
                        });
                    }
                    else {
                        var param = e.param;
                        param.formId = "stk_postockin";
                        that.Model.invokeFormOperation({
                            id: 'tbSave',
                            opcode: 'save',
                            param: param
                        });
                    }
                    break;
                case 'auditflow':
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    if (checkorder && that.remindnumbers) {
                        e.result = true;
                        yiDialog.d({
                            id: 'remindnumbers',
                            type: 1,
                            resize: false,
                            maxmin: false,
                            title: '系统提示',
                            content: that.remindnumbers,
                            area: ['400px', '200px'],
                            btn: ['确定'],
                            yes: function (index, layero) {
                                layer.close(index);
                            }
                        });
                    }
                    break;
            }
        }


        //检查订单是否满足条件
        _child.prototype.checkOrder = function (opname) {
            var that = this;
            var isremind = false;

            var selectedRows;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                selectedRows = [{ pkValue: that.Model.pkid }];
            }
            if (that.Model.viewModel.domainType == Consts.domainType.list) {
                selectedRows = that.Model.getSelectRows();
                if (!selectedRows || selectedRows.length <= 0) {
                    yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                    return;
                }
            }

            var ids = [];
            for (var i = 0; i < selectedRows.length; i++) {
                ids.push(selectedRows[i].pkValue);
            }
            var param = {
                simpleData: {
                    formId: 'stk_postockin',
                    Ids: ids.join(","),
                    opname: opname
                }
            };

            yiAjax.p('/bill/stk_postockin?operationno=verifyproduct', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (res.isSuccess && srvData) {
                    isremind = true;
                    that.remindnumbers = srvData;
                }
            }, null, null, null, { async: false });
            return isremind;
        };

        //条码联查
        _child.prototype.querybarcode = function (e) {
            debugger
            var that = this;
            e.result = true;

            var selRows = that.Model.getSelectRows({ id: that.fentity });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('请先选中行再操作!');
                return;
            }
            //if (selRows.length > 1) {
            //    yiDialog.warn('只允许勾选一行进行条码联查!');
            //    return;
            //}

            if (that.Model.pkid == '' || that.Model.pkid == undefined) {
                yiDialog.mt({ msg: '请保存单据', skinseq: 2 });
                return;
            }
            var datas = [];
            for (var i = 0; i < selRows.length; i++) {
                datas.push({ seldata: selRows[i].pkid });
            }
            //JSON.stringify(datas)
            that.Model.invokeFormOperation({
                id: 'isexsitbarcode',
                opcode: 'isexsitbarcode',
                param: {
                    'pkid': that.Model.pkid,
                    'formId': 'bcm_barcodemaster',
                    'fsourcetype': 'stk_postockin',
                    'fsourcelinenumber': JSON.stringify(datas),
                }
            });

        },


            // 选单
            _child.prototype.pull = function (e) {
                var that = this;
                /*
                1、当采购入库单.供应商不为空时
                a、点<选单>，进入《采购订单-列表》，需要自动匹配筛选 “ 采购订单的【供应商】+【采购员】+【采购部门】（特殊处理：若采购员或采购部门为空，则不参与过滤）等于采购入库单所选择的当前供应商、采购员、采购部门 且 采购订单商品明细行的【采购数量】大于 源单采购订单商品明细已关联采购入库单.入库存明细的【实收数量】汇总 ” 这一类采购入库单数据范围供选择；
                b、当采购入库单重新选择供应商，需要直接清空原已选择的所有源单信息；
                */
                var fsupplierid = that.Model.getSimpleValue({ id: "fsupplierid" });
                if (fsupplierid) {
                    e.param.fsupplierid = fsupplierid;
                }
                var fpostaffid = that.Model.getSimpleValue({ id: 'fpostaffid' });
                if (fpostaffid) {
                    e.param.fpostaffid = fpostaffid;
                }
                var fpodeptid = that.Model.getSimpleValue({ id: 'fpodeptid' });
                if (fpodeptid) {
                    e.param.fpodeptid = fpodeptid;
                }

            };

        // 清空源单信息
        _child.prototype.clearEntry = function () {
            var that = this;
            that.Model.deleteEntryData({ id: that.fentity });
            // 添加空行
            that.Model.addRow({ id: that.fentity });
        }

        //表格行创建后事件
        _child.prototype.onEntryRowCreated = function (e) {
            var that = this;

            //#34505需求
            var fbilltype = that.Model.getValue({ id: 'fbilltype' });
            if (fbilltype) {
                that.Model.invokeFormOperation({
                    id: 'querylock',
                    opcode: 'querylock',
                    param: {
                        'fid': fbilltype.id,
                        'ffieldid': "fpoprice",
                    }
                });
            }
        }


        //设置一件代发按钮显示隐藏
        _child.prototype.setDropShipMentMenuVisible = function () {

            var that = this;
            //debugger
            var fpiecesendtag = that.Model.getValue({ id: 'fpiecesendtag' });
            if (fpiecesendtag) {
                //that.Model.setVisible({ id: '[menu=save]', value: false });
                that.Model.setVisible({ id: '[menu=savesubmit]', value: false });
                that.Model.setVisible({ id: '[menu=saveaudit]', value: false });
                that.Model.setVisible({ id: '[menu=submit]', value: false });
                that.Model.setVisible({ id: '[menu=submitflow]', value: false });
                that.Model.setVisible({ id: '[menu=audit]', value: false });
                that.Model.setVisible({ id: '[menu=auditflow]', value: false });
                that.Model.setVisible({ id: '[menu=delete]', value: false });
                that.Model.setVisible({ id: '[menu=cancel]', value: false });
                that.Model.setVisible({ id: '[menu=change]', value: false });
            }

            if (Consts.isdirectsale) {
                setTimeout(function () { that.setFieldMustFlag({ id: "fsupplierid", caption: "供应商", must: false }); }, 200)

            } else {
                setTimeout(function () { that.setFieldMustFlag({ id: "fsupplierid", caption: "供应商", must: true }); }, 200)
            }
        }



        //设置一件代发显示隐藏
        //_child.prototype.setDropShipMentVisible = function () {

        //    var that = this;

        //    var flag = false;
        //    var billtypename = that.Model.getValue({ id: "fbilltype" })?.fname;
        //    if ((billtypename == "v6定制柜合同" || billtypename == "v6定制柜合同")) {
        //        flag = false;
        //    } else {
        //        var parm = {
        //        };
        //        yiAjax.p('/bill/ydj_order?operationno=getagentcustomchannel', parm, function (r) {
        //            if (r) {
        //                if (r.operationResult?.srvData) {
        //                    var fcustomchannel = r.operationResult.srvData["fcustomchannel"];
        //                    if (fcustomchannel == "1") {
        //                        flag = true;
        //                    }
        //                    var ftoppiecesendtag = r.operationResult.srvData["ftoppiecesendtag"];
        //                    if (Consts.isdirectsale && ftoppiecesendtag) {
        //                        flag = true;//不允许编辑
        //                    } else if (Consts.isdirectsale) {
        //                        flag = true;
        //                    }

        //                }
        //            }
        //        }, null, null, null, { async: false });
        //    }

        //    that.Model.setVisible({ id: '.dropshipping-info-tag', value: flag });//一件代发相关字段

        //}
        //设置一件代发显示隐藏
        _child.prototype.setDropShipMentVisible = function () {
            var that = this;
            var flag = false;
            var billtypename = that.Model.getValue({ id: "fbilltype" })?.fname;
            if ((billtypename == "v6定制柜合同" || billtypename == "v6定制柜合同")) {
                flag = false;
            } else {
                var parm = {
                };
                yiAjax.p('/bill/ydj_order?operationno=getagentcustomchannel', parm, function (r) {
                    if (r) {
                        if (r.operationResult?.srvData) {
                            var fcustomchannel = r.operationResult.srvData["fcustomchannel"];
                            if (fcustomchannel == "1") {
                                flag = true;
                            }
                            var ftoppiecesendtag = r.operationResult.srvData["ftoppiecesendtag"];
                            var fmanagemodel = r.operationResult.srvData["fmanagemodel"];
                            if (fmanagemodel == 1 && ftoppiecesendtag) {
                                flag = true;
                            } else if (fmanagemodel == 1 && !ftoppiecesendtag) {
                                flag = false;
                            }
                        }
                    }
                }, null, null, null, { async: false });
            }
            that.Model.setVisible({ id: '.dropshipping-info-tag', value: flag });//一件代发相关字段
        }


        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            var that = this;
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };

        return _child;
    })(BasePlugIn);
    window.stk_postockin = window.stk_postockin || stk_postockin;
})();