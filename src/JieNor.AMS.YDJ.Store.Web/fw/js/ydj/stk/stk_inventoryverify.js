///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/stk_inventoryverify.js
*/
; (function () {
    var stk_inventoryverify = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            that.remindnumbers = "";
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';
        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************
        //页面初始化
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            //初始化库存参数
            that.initStockSysParam();

            //对盘点方案生成的盘点单明细，锁定商品不允许变更
            var inventBase = that.Model.getSimpleValue({ id: 'finventbase' });
            if (inventBase && inventBase.length > 0) {
                var entryData = that.Model.getEntryData({ id: that.fentity });
                for (var i = 0; i < entryData.length; i++) {
                    if (entryData[i].fstockqty > 0) {
                        //手动新增行不会有账存数量
                        var row = entryData[i].id;
                        that.Model.setEnable({ id: 'fmaterialid', row: row, value: false });
                        //仓库仓位也不允许修改
                        that.Model.setEnable({ id: 'fstorehouseid', row: row, value: false });
                        that.Model.setEnable({ id: 'fstorelocationid', row: row, value: false });
                        that.Model.setEnable({ id: 'fstockstatus', row: row, value: false });
                    }
                }
            }
            that.setFieldMustFlagByDirectSale();
        };

        //初始化库存参数
        _child.prototype.initStockSysParam = function () {
            var that = this;

            that.Model.invokeFormOperation({
                id: 'loadstocksysparam',
                opcode: 'loadstocksysparam',
                param: {
                    'formId': 'stk_stockparam',
                    'domainType': 'parameter'
                }
            });
        }

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == that.fentity) {
                switch (e.btnid.toLowerCase()) {
                    case 'g_record': //批录按钮 
                        //仓位禁止批录，需求#35670 
                        if (e.fieldId.toLowerCase() === 'fstorehouseid') {
                            e.copyFields = ["fstockstatus"];
                            e.clearFields = ["fstorelocationid"];
                        }
                        break;
                }
            }
        };

        //批量转标准品 将当前页面的
        _child.prototype.showstandardcustombatch = function () {
            var that = this;
            //获取当前明细数据
            var rowData = that.Model.getEntryData({ id: that.fentity });
            that.Model.invokeFormOperation({
                id: 'standardbustombatch',
                opcode: 'standardbustombatch',
                param: {
                    formId: 'stk_inventoryverify',
                    allrows: JSON.stringify(rowData),
                    domainType: 'dynamic'
                }
            });
        };

        //标准定制
        _child.prototype.showstandardcustom = function () {
            var that = this;
            //选中行
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行标准定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('标准定制不支持多选！');
                return;
            };
            if (ds) {
                var attrinfo = ds[0].data.fattrinfo;
                //var rowData = that.Model.getEntryRowData({ id: that.fentity, row: attrinfo.row });
                ////选中行
                //判断物料是否启用 选配类别 
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且未勾选非标定制
                if (Isenableselectioncategory && !fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框 
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或勾选了非标定制，不允许标准定制！');
                    return;
                }
            }
        };

        //非标定制
        _child.prototype.showunstandardcustom = function () {
            var that = this;
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行非标定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('非标定制不支持多选！');
                return;
            }
            if (ds) {
                //判断选中行物料是否启用 选配类别
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //允许选配
                var fispresetprop = ds[0].data.fispresetprop;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且 勾选非标定制
                if (fispresetprop && fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或未勾选非标定制，不允许非标准定制！');
                    return;
                }
            }
        };


        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    that.aftersetauxProp(e);
                    break;
            }
        };

        //标准定制、非标定制后根据属性、属性值 匹配带出配件
        _child.prototype.aftersetauxProp = function (e) {
            var that = this;
            if (!e.value.fentity) {
                return;
            }
            var attrinfo = e.value;
            var attrinfoNo = attrinfo.fentity.filter(o => o.fvaluename == "无");
            that.attrinfoNew = [];
            if (attrinfoNo) {
                attrinfoNo.forEach(o => { that.attrinfoNew.push(o.fauxpropid.fname) });
            }
            var rowData = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            var productId = $.trim(rowData.fmaterialid && rowData.fmaterialid.id);
            if (attrinfo.fentity && attrinfo.fentity.length > 0) {
                var isCheckCustom = false;
                var ds = that.Model.getSelectRows({ id: that.fentity });
                if (ds.length > 0) {
                    var rowid = ds[0].data.id;
                    isCheckCustom = rowid == rowData.id;
                }
                if (isCheckCustom || rowData.fsuitcombnumber) isCheckCustom = true;

                if (isCheckCustom) {
                    that.Model.invokeFormOperation({
                        id: 'doaddparts_custom',
                        opcode: 'doaddparts_custom',
                        param: {
                            formId: 'stk_inventoryverify',
                            rows: JSON.stringify(attrinfo),
                            currentrow: JSON.stringify(rowData),
                            productId: productId,
                            rowId: e.row,
                            domainType: 'dynamic'
                        }
                    });
                }
            }
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.fentity:

                    break;
            }
        };
        //获取字段限额控制参数值
        _child.prototype.getFieldAstrictParam = function () {
            var that = this;
            let result = null;
            yiAjax.p('/parameter/bas_fieldastrictparam?operationno=queryFieldAstrictParam&pageid=' + that.Model.viewModel.pageId, null,
                function (r) {
                    result = r.operationResult.srvData;
                },
                null, null, null, { async: false }
            );
            return result;
        }

        //检验字段最大限额控制值
        _child.prototype.checkFieldParam = function () {
            debugger;
            var that = this;
            var fieldVerifyData = that.getFieldAstrictParam();
            var noticecontent = "";
            var productData = that.Model.getEntryData({ id: "fentity" });

            for (var i = 0; i < productData.length; i++) {
                const item = productData[i];
                if (fieldVerifyData.fmaxcheckunitprice > 0) {
                    if (item.fpdprice > fieldVerifyData.fmaxcheckunitprice) {
                        noticecontent += '第' + (i + 1) + '行商品【' + item.fmaterialid.fnumber + '】基本单位盘点单价过大,请检查是否填写正确!</br>';
                    }
                }
                if (fieldVerifyData.fmaxcheckcount > 0) {
                    if (item.fbizpdqty > fieldVerifyData.fmaxcheckcount) {
                        noticecontent += '第' + (i + 1) + '行商品【' + item.fmaterialid.fnumber + '】盘点数量过大,请检查是否填写正确!</br>';
                    }
                }
            }

            return noticecontent;
        }
        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            switch (e.opcode) {
                case "submitflow":
                    debugger
                    e.result = true;
                    if (that.Model.viewModel.domainType == Consts.domainType.bill) {

                        const noticecontent = that.checkFieldParam();
                        if (noticecontent) {
                            yiDialog.d({
                                type: 1,
                                resize: false,
                                content: '<div style="min-height:34px;"><i class="layui-layer-ico layui-layer-ico3" style="position: absolute;top: 66px;left: 15px;width: 30px;height: 30px;"></i><span style="margin-left:38px;min-height:34px;font-size:14px;line-height:24px;padding-top:3px;display: inline-block;">' + noticecontent + '</span></div>',
                                title: '提示',
                                area: '550px',
                                btn: ['取消', '确定'],
                                yes: function (index, layero) {
                                    layer.close(index);
                                    return false;
                                },
                                btn2: function (index, layero) {
                                    that.Model.invokeFormOperation({
                                        id: 'submitflow',
                                        opcode: 'submitflow',
                                        param: {
                                            formId: 'stk_inventoryverify',
                                        }
                                    })
                                    layer.close(index);
                                },
                                cancel: function (index, layero) {
                                    layer.close(index);
                                    return false;
                                }
                            });
                        } else {
                            that.Model.invokeFormOperation({
                                id: 'submitflow',
                                opcode: 'submitflow',
                                param: {
                                    formId: 'stk_inventoryverify',
                                }
                            })
                        }
                    } else {
                        that.Model.invokeFormOperation({
                            id: 'submitflow',
                            opcode: 'submitflow',
                            param: {
                                formId: 'stk_inventoryverify',
                            }
                        })
                    }

                    break;
                case 'inventrange':
                    //弹出即时库存列表
                    that.Model.showSelectForm({ formId: 'stk_inventorylist' });
                    e.result = true;
                    break;
                //标准定制
                case 'showstandardcustom':
                    e.result = true;
                    that.showstandardcustom();
                    break;

                //非标定制
                case 'showunstandardcustom':
                    e.result = true;
                    that.showunstandardcustom();
                    break;
                case 'showstandardcustombatch':
                    e.result = true;
                    that.showstandardcustombatch();
                    break;
                case 'querybarcode':
                    that.querybarcode(e);
                    break;
                case "printpybarcode":
                    var datas = [];
                    var selectedRows = that.Model.getAllSelectedRows({ id: "fentity" });
                    if (!selectedRows || selectedRows.length == 0) {
                        yiDialog.mt({ msg: '未选中行! ', skinseq: 2 });
                        e.result = true;
                        break;
                    }
                    if (selectedRows.length > 1) {
                        yiDialog.mt({ msg: '只允许勾选一行进行条码联查!', skinseq: 2 });
                        e.result = true;
                        break;
                    }
                    for (var i = 0; i < selectedRows.length; i++) {
                        datas.push({ fentryid: selectedRows[i].pkid });
                    }
                    e.param.datas = JSON.stringify(datas);
                    break;
                //获取成本
                case "receiveamount":
                    that.receiveamount(e);
                    break;
                //获取零成本
                case "":
                    that.receiveretailprice(e);
                    break;
                case 'save':
                    // 商品档案勾选了<允许选配>或<非标产品>校验合同辅助属性和定制说明,
                    var productEntry = that.Model.getEntryData({ id: that.fentity });
                    var checkEntry = [];
                    var productGroup = [];
                    var errProductNo = "";
                    for (var i = 0; i < productEntry.length; i++) {
                        var attrinfoEntity = productEntry[i].fattrinfo.fentity;
                        if (attrinfoEntity == null) attrinfoEntity = [];
                        var entryitem = {
                            fproductid: productEntry[i].fmaterialid.id,
                            fseq: productEntry[i].FSeq,
                            fattrinfo: attrinfoEntity.length > 0 ? JSON.stringify(attrinfoEntity) : "",
                            fcustomdesc: productEntry[i].fcustomdesc,
                        };
                        checkEntry.push(entryitem);

                        debugger;
                        //对比之前的商品行，检查【单位体积】是否一致
                        var checkObj = {
                            fmaterialid: productEntry[i].fmaterialid.id,
                            fattrinfo: productEntry[i].fattrinfo.id,
                            fcustomdesc: productEntry[i].fcustomdesc,
                            funitid: productEntry[i].funitid.id,
                            fsinglevolume: productEntry[i].fsinglevolume
                        }
                        var isPush = true, isError = false;
                        for (var j = 0; j < productGroup.length; j++) {
                            if (productGroup[j].fmaterialid == checkObj.fmaterialid
                                && productGroup[j].fattrinfo == checkObj.fattrinfo
                                && productGroup[j].fcustomdesc == checkObj.fcustomdesc
                                && productGroup[j].funitid == checkObj.funitid) {
                                //商品、辅助属性、定制说明、基本单位都能对应上
                                isPush = false;
                                if (productGroup[j].fsinglevolume != checkObj.fsinglevolume) {
                                    //单位体积不一致，则需要提示用户
                                    isError = true;
                                }
                                break;
                            }
                        }
                        if (isError) {
                            if (errProductNo.length > 0) {
                                errProductNo += "，";
                            }
                            errProductNo += productEntry[i].fmaterialid.fnumber;
                        }
                        if (isPush) {
                            productGroup.push(checkObj);
                        }
                    }
                    var param = {
                        simpleData: {
                            formId: 'stk_inventoryverify',
                            entry: JSON.stringify(checkEntry),
                            status: that.Model.getSimpleValue({ id: 'fstatus' }),
                            domainType: 'dynamic'
                        }
                    };
                    var fstatus = that.Model.getValue({ id: "fstatus" });
                    e.param.fstatus = fstatus?.id
                    if (errProductNo.length > 0) {
                        e.result = true;
                        yiDialog.c("商品【" + errProductNo + "】体积不一致，您确定继续吗？", function () {
                            that.checkproducts(e, param);
                        }, function () {
                            //取消
                            e.result = true;
                        }, '温馨提示', function () {
                            //直接关闭
                            e.result = true;
                        });
                    }
                    else {
                        that.checkproducts(e, param);
                    }
                    break;
                case 'saveaudit':
                case 'rejectflow':
                case 'auditflow':
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    if (checkorder && that.remindnumbers) {
                        e.result = true;
                        yiDialog.d({
                            id: 'remindnumbers',
                            type: 1,
                            resize: false,
                            maxmin: false,
                            title: '系统提示',
                            content: that.remindnumbers,
                            area: ['400px', '200px'],
                            btn: ['确定'],
                            yes: function (index, layero) {
                                layer.close(index);
                            }
                        });
                    }
                    break;
            }
        };

        //检查订单是否满足条件
        _child.prototype.checkOrder = function (opname) {
            var that = this;
            var isremind = false;

            var selectedRows;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                selectedRows = [{ pkValue: that.Model.pkid }];
            }
            if (that.Model.viewModel.domainType == Consts.domainType.list) {
                selectedRows = that.Model.getSelectRows();
                if (!selectedRows || selectedRows.length <= 0) {
                    yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                    return;
                }
            }

            var ids = [];
            for (var i = 0; i < selectedRows.length; i++) {
                ids.push(selectedRows[i].pkValue);
            }
            var param = {
                simpleData: {
                    formId: 'stk_inventoryverify',
                    Ids: ids.join(","),
                    opname: opname
                }
            };

            yiAjax.p('/bill/stk_inventoryverify?operationno=verifyproduct', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (res.isSuccess && srvData) {
                    isremind = true;
                    that.remindnumbers = srvData;
                }
            }, null, null, null, { async: false });
            return isremind;
        };

        //获取成本
        _child.prototype.receiveamount = function (e) {
            var that = this;
            e.result = true;
            var selRows = that.Model.getSelectRows({ id: "fentity" });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('请先勾选商品行后再获取。');
                return;
            }
            var datas = [];
            debugger
            for (var i = 0; i < selRows.length; i++) {
                datas.push({
                    fmaterialid: selRows[i].data.fmaterialid.id,
                    fattrinfo: selRows[i].data.fattrinfo.id,
                    fattrinfo_e: selRows[i].data.fattrinfo_e,
                    fcustomdesc: selRows[i].data.fcustomdesc,
                    funitid: selRows[i].data.funitid.id,
                    fmtrlnumber: selRows[i].data.fmtrlnumber,
                    pkid: selRows[i].pkid
                });
            }
            var fmainorgid = that.Model.getSimpleValue({ id: 'fmainorgid' });
            that.Model.invokeFormOperation({
                id: 'btnreceiveamount',
                opcode: 'receiveamount',
                selectedRows: selRows.pkid,
                param: {
                    data: JSON.stringify(datas),
                    fmainorgid: fmainorgid
                }
            });
        };


        //获取零成本
        _child.prototype.receiveretailprice = function (e) {
            var that = this;
            e.result = true;
            var selRows = that.Model.getSelectRows({ id: "fentity" });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('请先勾选商品行后再获取。');
                return;
            }
        };

        //条码联查
        _child.prototype.querybarcode = function (e) {
            var that = this;
            e.result = true;

            var selRows = that.Model.getSelectRows({ id: that.fentity });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('请先选中行再操作!');
                return;
            }
            //if (selRows.length > 1) {
            //    yiDialog.warn('只允许勾选一行进行条码联查!');
            //    return;
            //}
            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            if (fbillno == '' || fbillno == undefined) {
                yiDialog.mt({ msg: '请保存单据', skinseq: 2 });
                return;
            }
            var datas = [];
            for (var i = 0; i < selRows.length; i++) {
                datas.push({ seldata: selRows[i].data.fmaterialid.id });
            }
            //JSON.stringify(datas)
            that.Model.invokeFormOperation({
                id: 'isexsitbarcode',
                opcode: 'isexsitbarcode',
                param: {
                    'formId': 'bcm_barcodemaster',
                    'fsourcetype': 'stk_inventoryverify',
                    'fsourcenumber': fbillno,
                    'fmaterialid': JSON.stringify(datas),
                }
            });
        };


        //多选列表选择后的数据。
        _child.prototype.onAfterSelectFormData = function (e) {
            if (!e || !e.formId) return;
            var that = this;
            switch (e.formId) {
                case 'stk_inventorylist':
                    e.result = true;
                    if (!e.data || e.data.length <= 0) return;
                    var selRows = [];
                    e.data.map(function (item) {
                        selRows.push({
                            PKValue: item.fbillhead_id
                        });
                    });
                    that.Model.invokeFormOperation({
                        id: 'loadinventorylist',
                        opcode: 'loadinventorylist',
                        selectedRows: selRows,
                        param: {
                            formId: 'stk_inventorylist',
                            domainType: Consts.domainType.bill
                        }
                    });
                    break;
            }
        };
        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            switch (e.id.toLowerCase()) {
                case 'funstdtype':
                    //允许选配 (商品档案未勾选允许选配)下单时不允许勾选非标选项 - PC端
                    var fispresetprop = that.Model.getSimpleValue({ id: "fispresetprop", row: e.row });
                    var fcustom = that.Model.getSimpleValue({ id: "fcustom", row: e.row });
                    if (!fispresetprop && !fcustom) {
                        e.result.enabled = false;
                    } else {
                        e.result.enabled = true;
                    }
                    // 如果勾选上【是否非标】后, 不允许取消勾选, 只允许删除
                    if (e.value.funstdtype) {
                        e.result.enabled = false;
                    }
                    else {
                        e.result.enabled = true;
                    }
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            debugger;
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'loadinventorylist':
                    that.fillEntryRow(srvData);
                    break;
                case 'doaddparts_custom':
                    if (isSuccess && srvData) {
                        //如果匹配到标准品商品id则直接更新商品
                        if (srvData.standardId && srvData.standardId != undefined) {
                            //// 触发获取商品其他信息
                            var row = that.Model.getEntryRowData({ id: that.fentity, row: srvData.rowId });
                            that.Model.setValue({ id: 'fmaterialid', row: srvData.rowId, value: srvData.standardId });
                            that.Model.setValue({ id: 'fdostandard', row: srvData.rowId, value: 1 });
                        }
                    }
                    break;
                case 'standardbustombatch':
                    if (isSuccess && srvData) {
                        //如果匹配到标准品商品id则直接更新商品
                        if (srvData.length > 0) {
                            for (var a = 0; a < srvData.length; a++) {
                                that.Model.setValue({ id: 'fmaterialid', row: srvData[a].rowId, value: srvData[a].StandardId });
                                that.Model.setValue({ id: 'fdostandard', row: srvData[a].rowId, value: 1 });
                            }
                        }
                    }
                    break;
                //case 'isexsitbarcode':
                //    //if (!isSuccess) {
                //    //    yiDialog.warn("当前商品未查询到对应条码信息!");
                //    //    return false;
                //    //}
                //    //弹出《条码主档》列表界面
                //    var filterString = "fmainorgid = '{0}' and fid in ({1})"
                //        .format(Consts.loginCompany.id, srvData.split(','));
                //    that.Model.showForm({
                //        formId: 'bcm_barcodemaster',
                //        domainType: Consts.domainType.list,
                //        param: {
                //            openStyle: Consts.openStyle.modal,
                //            filterstring: filterString
                //        }
                //    });
                //    break;
                case 'pack':
                    if (!isSuccess) {
                        that.Model.invokeFormOperation({
                            id: 'push',
                            opcode: 'push',
                            param: {
                                ruleid: "stk_inventoryverify2bcm_packorder"
                            }
                        });
                    }
                    break;
                case 'packinit':
                    if (!isSuccess) {
                        that.Model.invokeFormOperation({
                            id: 'push',
                            opcode: 'push',
                            param: {
                                ruleid: "stk_inventoryverify2bcm_packorderinit"
                            }
                        });
                    }
                    break;
                case 'getprices':
                    if (!srvData) {
                        break;
                    }
                    for (var i = 0, l = srvData.length; i < l; i++) {
                        var lm = srvData[i];

                        if (lm.success) {
                            //价格匹配成功，则赋值
                            that.Model.setValue({ id: 'fpdprice', row: lm.clientId, value: lm.purPrice });
                            that.Model.setValue({ id: 'fbizpdprice', row: lm.clientId, value: lm.purPrice });
                        }
                        else {
                            //价格匹配不成功
                            that.Model.setValue({ id: 'fpdprice', row: lm.clientId, value: 0 });
                            that.Model.setValue({ id: 'fbizpdprice', row: lm.clientId, value: 0 });
                        }
                    }

                    break;
                case 'loadstocksysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var stockSysParam = {};
                        for (var data in srvData) {
                            stockSysParam[data] = srvData[data];
                        }
                        localStorage.setItem("stocksysparam", JSON.stringify(stockSysParam));
                    }
                    break;

                //获取成本
                case "receiveamount":
                    var selRows = that.Model.getSelectRows({ id: "fentity" });
                    if (!isSuccess) {
                        var message = "";
                        for (var i = 0; i < selRows.length; i++) {
                            message += "第" + selRows[i].data.FSeq + "行编码为" + selRows[i].data.fmtrlnumber + "的商品获取失败，请检查处理。</br>";
                        }
                        yiDialog.warn(message);
                        return;
                    }

                    for (var i = 0; i < selRows.length; i++) {
                        for (var j = 0; j < srvData.length; j++) {
                            if (selRows[i].pkid == srvData[j]["id"]) {
                                var fbizpyqty = selRows[i].data.fbizpyqty;//盘盈数量
                                var fbizpkqty = selRows[i].data.fbizpkqty;//盘亏数量
                                var fbizpdqty = selRows[i].data.fbizpdqty;//盘点数量

                                var fpurfacprice = srvData[j].entitylist.fpurfacprice;//采购单价（折前）
                                var funifysaleprice = srvData[j].entitylist.funifysaleprice;//统一零售价（折前）
                                //var fsellprice = srvData[j].entitylist.fsellprice;//经销价（折前）
                                //var price = funifysaleprice == 0 ? fsellprice : funifysaleprice;
                                //that.Model.setValue({ id: 'fpdprice', value: fpurfacprice, row: selRows[i].pkid }); //基本单位盘点单价
                                //that.Model.setValue({ id: 'fcostprice', value: fpurfacprice, row: selRows[i].pkid }); //单位成本
                                //that.Model.setValue({ id: 'flamount', value: price, row: selRows[i].pkid }); //零售价
                                //that.Model.setValue({ id: 'fcostamt', value: fpurfacprice * fbizpdqty, row: selRows[i].pkid }); //总成本
                                //that.Model.setValue({ id: 'fpycbamount', value: fpurfacprice * fbizpyqty, row: selRows[i].pkid });//盘盈成本
                                //that.Model.setValue({ id: 'fpkcbamount', value: fpurfacprice * fbizpkqty, row: selRows[i].pkid });//盘亏成本

                                //【采购单价(折前) 】x【盘盈数量 - 盘亏数量】自动计算出【盈亏采购总额】
                                //【统一零售价】x【盘盈数量-盘亏数量】自动计算出【盈亏零售总额】
                                var fpkbuyamount = fpurfacprice * (fbizpyqty - fbizpkqty);//盈亏采购总额
                                var fpksaleamount = funifysaleprice * (fbizpyqty - fbizpkqty);//盈亏零售总额

                                that.Model.setValue({ id: 'fbugunitprice', value: fpurfacprice, row: selRows[i].pkid }); //盘点单.采购单价(折前)
                                that.Model.setValue({ id: 'funifyamount', value: funifysaleprice, row: selRows[i].pkid }); //盘点单.统一零售价
                                that.Model.setValue({ id: 'fpkbuyamount', value: fpkbuyamount, row: selRows[i].pkid }); //盈亏采购总额
                                that.Model.setValue({ id: 'fpksaleamount', value: fpksaleamount, row: selRows[i].pkid }); //盈亏零售总额

                                var fbilltype = that.Model.getSimpleValue({ id: 'fbilltype' });
                                //如果【单据类型】=“期初盘点单”，则还需将取到的【采购单价(折前)】填充到【基本单位盘点单价】字段
                                if (fbilltype == "inventoryverify_billtype_02") {
                                    that.Model.setValue({ id: 'fpdprice', value: fpurfacprice, row: selRows[i].pkid });//基本单位盘点单价
                                }
                            }
                        }
                    }

                    break;
                //获取零成本
                case "":
                    that.receiveretailprice(e);
                    break;
            }
        };

        //填充盘点明细行
        _child.prototype.fillEntryRow = function (srvData) {
            var that = this;
            if (!srvData || srvData.length <= 0) return;
            var currentDatas = that.Model.getValue({ id: that.fentity });
            for (var i = 0, l = srvData.length; i < l; i++) {
                compareEntryData(srvData[i]);
            }

            //检查指定的库存明细是否在当前盘点明细中存在，如果不存在则将指定的库存明细转换为盘点明细行
            function compareEntryData(invData) {

                //所有匹配维度字段值都相同，则认为已存在
                //匹配维度字段：商品，辅助属性，仓库，仓位，基本单位，库存单位，货主，定制说明，批号，定制跟踪号
                var exists = false;
                for (var i = 0, l = currentDatas.length; i < l; i++) {
                    var cd = currentDatas[i];
                    if ($.trim(cd.fmaterialid && cd.fmaterialid.id) === $.trim(invData.fmaterialid && invData.fmaterialid.id)
                        && $.trim(cd.fattrinfo && cd.fattrinfo.id) === $.trim(invData.fattrinfo && invData.fattrinfo.id)
                        && $.trim(cd.fstorehouseid && cd.fstorehouseid.id) === $.trim(invData.fstorehouseid && invData.fstorehouseid.id)
                        && $.trim(cd.fstorelocationid && cd.fstorelocationid.id) === $.trim(invData.fstorelocationid && invData.fstorelocationid.id)
                        && $.trim(cd.funitid && cd.funitid.id) === $.trim(invData.funitid && invData.funitid.id)
                        && $.trim(cd.fstockunitid && cd.fstockunitid.id) === $.trim(invData.fstockunitid && invData.fstockunitid.id)
                        && $.trim(cd.fownerid && cd.fownerid.id) === $.trim(invData.fownerid && invData.fownerid.id)
                        && $.trim(cd.fcustomdesc) === $.trim(invData.fcustomdesc)
                        && $.trim(cd.flotno) === $.trim(invData.flotno)
                        && $.trim(cd.fmtono) === $.trim(invData.fmtono)) {
                        exists = true;
                        break;
                    }
                }
                if (!exists) {
                    var newData = {
                        fmtrlnumber: invData.fmtrlnumber, //商品编码
                        fmaterialid: invData.fmaterialid, //商品名称
                        fmtrlmodel: invData.fmtrlmodel, //规格型号
                        fattrinfo: invData.fattrinfo, //辅助属性
                        fstorehouseid: invData.fstorehouseid, //仓库
                        fstorelocationid: invData.fstorelocationid, //仓位
                        funitid: invData.funitid, //基本单位
                        fbizunitid: invData.fbizunitid, //业务单位
                        fstockunitid: invData.fstockunitid, //库存单位
                        fownertype: invData.fownertype, //货主类型
                        fownerid: invData.fownerid, //货主
                        fcustomdesc: invData.fcustomdesc, //定制说明
                        flotno: invData.flotno, //批号
                        fmtono: invData.fmtono, //定制跟踪号
                        fstockstatus: invData.fstockstatus, //库存状态
                        fpderror: invData.fqty > 0 ? 100 : 0 //盘点误差
                    };
                    var newRowId = that.Model.addRow({ id: that.fentity, data: newData });

                    //给基本单位数量字段赋值，以便触发多单位换算逻辑
                    that.Model.setValue({ id: 'fqty', value: invData.fqty, row: newRowId }); //基本单位账存数量
                    that.Model.setValue({ id: 'fpkqty', value: invData.fqty, row: newRowId }); //基本单位盘亏数量
                }
            }
        };


        //初始化商品明细表的经销价插件
        _child.prototype.onBillInitProduct = function (param, params) {
            var that = this;

            var stockSysParam = JSON.parse(localStorage.getItem("stocksysparam"));
            var fverifyprice = stockSysParam ? stockSysParam.fverifyprice : false;
            if (fverifyprice == undefined || fverifyprice == false) {
                return;
            }

            // 如果有盘点方案，不取价格
            var finventbase = $.trim(that.Model.getSimpleValue({ id: 'finventbase' }));
            if (finventbase != '' && finventbase != undefined) {
                return;
            }

            var rowData = that.Model.getEntryRowData({ id: that.fentity, row: params.attrinfo.row });
            if (!rowData) return;
            if (JSON.stringify(rowData) == '{}') {
                return;
            }

            //如果商品没值，就不用取价，直接设置为0
            if (params.attrinfo.id == 'fmaterialid' && params.attrinfo.value && !$.trim(params.attrinfo.value.id)) {
                //that.Model.setValue({ id: 'fpdprice', row: rowData.id, value: 0 });
                //that.Model.setValue({ id: 'fbizpdprice', row: rowData.id, value: 0 });
                return;
            }

            //如果商品没值，所携带的辅助属性肯定也为空，直接设置为0
            if (params.attrinfo.id == 'fattrinfo' && rowData.fmaterialid && !$.trim(rowData.fmaterialid.id)) {
                //that.Model.setValue({ id: 'fpdprice', row: rowData.id, value: 0 });
                //that.Model.setValue({ id: 'fbizpdprice', row: rowData.id, value: 0 });
                return;
            }

            var reAttrinofEntry = [];//按照接口，重新组合
            var tempAttr = [];
            if (rowData.fattrinfo && rowData.fattrinfo.fentity && rowData.fattrinfo.fentity.length > 0) {
                tempAttr = rowData.fattrinfo.fentity;
            }

            for (var n = 0, m = tempAttr.length; n < m; n++) {
                reAttrinofEntry.push({
                    valueId: tempAttr[n].fvalueid,
                    auxPropId: tempAttr[n].fauxpropid.id
                })
            }

            //单位
            var funitid = $.trim(rowData.funitid && rowData.funitid.id);

            var productData = [];
            productData.push({
                clientId: rowData.id,
                productId: rowData.fmaterialid.id,
                unitId: funitid,
                bizDate: that.Model.getValue({ id: 'fdate' }),
                supplierNotFilter: true,//
                isReHqPurPrice: true,//
                supplierId: '',
                length: rowData.flength,
                width: rowData.fwidth,
                thick: rowData.fthick,
                attrInfo: {
                    id: '',
                    entities: reAttrinofEntry
                }
            });

            var option = { pull: params && params.pull };

            that.invokeGetPrice(param, productData, option);
        }


        //调用取价接口
        _child.prototype.invokeGetPrice = function (opId, productInfos, option) {
            this.Model.invokeFormOperation({
                id: opId,
                opcode: 'getprices',
                opctx: { option: option },
                param: {
                    formId: 'ydj_price',
                    domainType: 'dynamic',
                    priceFlag: 5,
                    productInfos: JSON.stringify(productInfos)
                }
            });
        };


        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    that.onBillInitProduct('change', { attrinfo: e });
                    break;
            }
        };


        //字段值发生变化后触发的事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            if (!e.row) return;
            switch (e.id.toLowerCase()) {
                case 'fmaterialid':
                    that.onBillInitProduct('change', { attrinfo: e });

                    that.setInDate(e.row);
                    that.Model.setValue({ id: 'fattrinfo', row: e.row, value: { fentity: [] }, ctx: { tgFatchPrice: false } });
                    that.Model.setValue({ id: 'fattrinfo_e', row: e.row, value: { fentity: [] }, ctx: { tgFatchPrice: false } });
                    break;
                case 'funitid':
                    that.onBillInitProduct('change', { attrinfo: e });

                    that.setInDate(e.row);
                    break;
                case 'fdate':
                    that.setInDate();
                    break;
                case 'finventbase':
                    that.setFieldMustFlagByDirectSale();
                    break;
            }
        };

        //将【盘点日期】填充到【入库日期】
        _child.prototype.setInDate = function (rowId) {
            var that = this;
            var fdate = that.Model.getValue({ id: 'fdate' });

            if (rowId) {
                that.Model.setValue({ id: 'findate', value: fdate, row: rowId });
            }
            else {
                var rowData = that.Model.getEntryData({ id: that.fentity });

                for (var i = 0; i < rowData.length; i++) {
                    var row = rowData[i];

                    that.Model.setValue({ id: 'findate', value: fdate, row: row.id });
                }
            }
        }

        _child.prototype.checkproducts = function (e, param) {
            var that = this;
            yiAjax.p('/bill/stk_inventoryverify?operationno=checkproducts', param, function (r) {
                var res = r.operationResult;
                e.result = true;
                var param = e.param;
                param.formId = "stk_inventoryverify";
                if (res.isSuccess && res.srvData.length > 0) {
                    yiDialog.c(res.srvData, function () {
                        that.Model.invokeFormOperation({
                            id: 'tbSave',
                            opcode: 'save',
                            param: param
                        });
                    });
                }
                else {
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: param
                    });
                }
            }, null, null, null, { async: false });
        };

        //设置必录标签
        _child.prototype.setFieldMustFlagByDirectSale = function (e) {
            var that = this;
            if (Consts.isdirectsale) {
                var finventbase = $.trim(that.Model.getSimpleValue({ id: 'finventbase' }));
                that.Model.setVisible({ id: '.zyclass', value: true }); //显示直营字段
                that.Model.setVisible({ id: 'fzyonwayqty', value: true });
                setTimeout(function () {
                    if (finventbase != '' && finventbase != undefined) {
                        that.Model.setEnable({ id: 'fstore', value: false });
                    } else {
                        that.Model.setEnable({ id: 'fstore', value: true });//允许编辑
                        that.Model.setValue({ id: 'fzytype', value: '2' });
                    }
                }, 100);
            } else {
                that.Model.setVisible({ id: '.zyclass', value: false }); //显示直营字段
                that.Model.setVisible({ id: 'fzyonwayqty', value: true });
            }
        };

        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            var that = this;
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            debugger
            switch (e.id.toLowerCase()) {
                //仓库：按库存参数中控制可选仓库范围
                case 'fstorehouseid':
                    if (Consts.isdirectsale) {//直营，只允许选择总仓或门店仓
                        var store = that.Model.getValue({ id: "fstore" });
                        if (store) {
                            e.result.filterString = "fmulstore like '%" + store + "%' AND fwarehousetype ('warehouse_01','warehouse_02','warehouse_04') ";
                        } else {
                            // ② 盘点门店为空
                            e.result.filterString = "fmulstore='' AND fwarehousetype ('warehouse_01','warehouse_02') ";
                        }
                    }
                    break;
            }
        };



        return _child;
    })(BasePlugIn);
    window.stk_inventoryverify = window.stk_inventoryverify || stk_inventoryverify;
})();