/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/stk/stk_sostockreturn.js
 */
; (function () {
    var stk_sostockreturn = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.filesavemsg = [];//全局存储信息
            that.result = [];
            that.tempId = [];
            that.succArr = [];
            that.nowRow = '';//当前被双击的行
            that.suentry = [];
            that.remindnumbers = "";
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';//商品信息

        //初始化动态表单插件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            var returntype = that.Model.getSimpleValue({ id: 'freturntype' });
            if (returntype == "sostockreturn_biztype_02") {
                that.Model.setEnable({ id: 'factualreturnamount', value: true });
            } else {
                that.Model.setEnable({ id: 'factualreturnamount', value: false });
            }
            that.setDropShipMentVisible();
        };

        //页面视图初始化后事件
        _child.prototype.onViewInitialized = function (args) {
            var that = this;
            var returntype = that.Model.getSimpleValue({ id: 'freturntype' });
            if (returntype == "sostockreturn_biztype_02") {
                that.Model.setEnable({ id: 'factualreturnamount', value: true });
            } else {
                that.Model.setEnable({ id: 'factualreturnamount', value: false });
            }
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.fentity:

                    break;
            }
        };

        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            //退货退款不携带物流跟踪号到销售退货单
            if (that.formContext.status == 'push' &&
                that.Model.getSimpleValue({ id: 'fsourcetype' }) == 'stk_sostockout' &&
                that.Model.getValue({ id: 'fplanreturnamount' }) > 0) {
                var dataEntry = that.Model.getEntryData({ id: that.fentity });
                for (var i = 0, j = dataEntry.length; i < j; i++) {
                    that.Model.setValue({ id: 'fmtono', row: dataEntry[i].id, value: '' });
                }
            }

            that.LoadElementEnable();
            var fstatus = that.Model.getSimpleValue({ id: 'fstatus' });
            //如果是提交、审核不走以下逻辑。
            if (that.formContext && that.formContext.cp && that.formContext.cp.type && that.formContext.cp.type == 'pull') {
                that.LoadAmount();
            }
        };

        // 选单
        _child.prototype.pull = function (e) {
            var that = this;
            var sourceNumber = that.Model.getValue({ id: 'fsourcenumber' });
            if (sourceNumber) {
                e.param.sourceNumber = sourceNumber;
            }
        };

        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            switch (args.opcode) {
                case 'pull':
                    that.pull(args);
                    break;
                case "searchbarcode":
                    var datas = [];
                    var selectedRows = that.Model.getAllSelectedRows({ id: "fentity" });
                    if (!selectedRows || selectedRows.length == 0) {
                        yiDialog.mt({ msg: '请先选中行再操作!', skinseq: 2 });
                        args.result = true;
                        break;
                    }
                    //if (selectedRows.length > 1) {
                    //    yiDialog.mt({ msg: '只允许勾选一行进行条码联查!', skinseq: 2 });
                    //    args.result = true;
                    //    break;
                    //}
                    for (var i = 0; i < selectedRows.length; i++) {
                        datas.push({ fentryid: selectedRows[i].pkid });
                    }
                    args.param.datas = JSON.stringify(datas);
                    break;
                //标准定制
                case 'showstandardcustom':
                    args.result = true;
                    that.showstandardcustom();
                    break;
                //非标定制
                case 'showunstandardcustom':
                    args.result = true;
                    that.showunstandardcustom();
                    break;
                //退款
                case 'refund':
                    args.result = true;
                    var fsourcenumber = that.Model.getValue({ id: "fsourcenumber" });
                    if (fsourcenumber) {
                        var ftype = that.Model.getValue({ id: 'fsourcetype' });
                        if (ftype.fnumber != "stk_sostockout") {
                            yiDialog.warn('源单类型不是销售出库单，暂不支持退款！');
                            return;
                        }
                    } else {
                        var actualreturnamount = that.Model.getSimpleValue({ id: "factualreturnamount" });
                        if (actualreturnamount == 0) {
                            yiDialog.warn('当前订单实退款金额为0元，不能退款！');
                            return;
                        }
                    }
                    that.Model.invokeFormOperation({
                        id: 'LoadSettleInfo',
                        opcode: 'LoadSettleInfo',
                        selectedRows: [{ PKValue: that.Model.pkid }],
                        param: {
                            settleType: args.opcode
                        }
                    });
                    break;
                case 'save':
                case 'savesubmit':
                    args.result = true;
                    that.checkProducts(args);
                    break;
                case 'saveaudit':
                    args.result = true;
                    var fbacktohq = that.Model.getValue({ id: "fbacktohq" });
                    if (Consts.isdirectsale && args.opcode.toLowerCase() == 'saveaudit' && !fbacktohq) {
                        yiDialog.c('审核通过后自动同步总部且不可反审核，确定继续审核？', function () {
                            var checkorder = that.checkOrder(args.opcode.toLowerCase());
                            if (checkorder && that.remindnumbers) {
                                yiDialog.d({
                                    id: 'remindnumbers',
                                    type: 1,
                                    resize: false,
                                    maxmin: false,
                                    title: '系统提示',
                                    content: that.remindnumbers,
                                    area: ['400px', '200px'],
                                    btn: ['确定'],
                                    yes: function (index, layero) {
                                        layer.close(index);
                                    }
                                });
                            }
                            else {
                                that.checkProducts(args);
                            }
                        }, function () {
                            args.result = true;
                            return;
                        });
                    } else {
                        var checkorder = that.checkOrder(args.opcode.toLowerCase());
                        if (checkorder && that.remindnumbers) {
                            yiDialog.d({
                                id: 'remindnumbers',
                                type: 1,
                                resize: false,
                                maxmin: false,
                                title: '系统提示',
                                content: that.remindnumbers,
                                area: ['400px', '200px'],
                                btn: ['确定'],
                                yes: function (index, layero) {
                                    layer.close(index);
                                }
                            });
                        }
                        else {
                            that.checkProducts(args);
                        }
                    }
                    break;
                case 'rejectflow':
                case 'auditflow':

                    var fbacktohq = that.Model.getValue({ id: "fbacktohq" });
                    if (Consts.isdirectsale && args.opcode.toLowerCase() == 'auditflow' && !fbacktohq) {
                        args.result = true;
                        yiDialog.c('审核通过后自动同步总部且不可反审核，确定继续审核？', function () {
                            var checkorder = that.checkOrder(args.opcode.toLowerCase());
                            if (checkorder && that.remindnumbers) {
                                args.result = true;
                                yiDialog.d({
                                    id: 'remindnumbers',
                                    type: 1,
                                    resize: false,
                                    maxmin: false,
                                    title: '系统提示',
                                    content: that.remindnumbers,
                                    area: ['400px', '200px'],
                                    btn: ['确定'],
                                    yes: function (index, layero) {
                                        layer.close(index);
                                    }
                                });
                            } else {
                                var param = args.param;
                                param.formId = "stk_sostockreturn";
                                that.Model.invokeFormOperation({
                                    id: 'tbAudit',
                                    opcode: args.opcode.toLowerCase(),
                                    param: param
                                });
                            }
                        }, function () {
                            args.result = true;
                            return;
                        });
                    }
                    else {
                        var checkorder = that.checkOrder(args.opcode.toLowerCase());
                        if (checkorder && that.remindnumbers) {
                            args.result = true;
                            yiDialog.d({
                                id: 'remindnumbers',
                                type: 1,
                                resize: false,
                                maxmin: false,
                                title: '系统提示',
                                content: that.remindnumbers,
                                area: ['400px', '200px'],
                                btn: ['确定'],
                                yes: function (index, layero) {
                                    layer.close(index);
                                }
                            });
                        }
                    }

                    break;
            }
        }

        //检查订单是否满足条件
        _child.prototype.checkOrder = function (opname) {
            var that = this;
            var isremind = false;

            var selectedRows;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                selectedRows = [{ pkValue: that.Model.pkid }];
            }
            if (that.Model.viewModel.domainType == Consts.domainType.list) {
                selectedRows = that.Model.getSelectRows();
                if (!selectedRows || selectedRows.length <= 0) {
                    yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                    return;
                }
            }

            var ids = [];
            for (var i = 0; i < selectedRows.length; i++) {
                ids.push(selectedRows[i].pkValue);
            }
            var param = {
                simpleData: {
                    formId: 'stk_sostockreturn',
                    Ids: ids.join(","),
                    opname: opname
                }
            };

            yiAjax.p('/bill/stk_sostockreturn?operationno=verifyproduct', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (res.isSuccess && srvData) {
                    isremind = true;
                    that.remindnumbers = srvData;
                }
            }, null, null, null, { async: false });
            return isremind;
        };

        _child.prototype.checkProducts = function (e) {
            // 商品档案勾选了<允许选配>或<非标产品>校验合同辅助属性和定制说明,
            var that = this;
            var productEntry = that.Model.getEntryData({ id: that.fentity });
            var checkEntry = [];
            for (var i = 0; i < productEntry.length; i++) {
                var attrinfoEntity = productEntry[i].fattrinfo.fentity;
                if (attrinfoEntity == null) attrinfoEntity = [];
                var entryitem = {
                    fproductid: productEntry[i].fmaterialid.id,
                    fseq: productEntry[i].FSeq,
                    fattrinfo: attrinfoEntity.length > 0 ? JSON.stringify(attrinfoEntity) : "",
                    fcustomdesc: productEntry[i].fcustomdesc,
                };
                checkEntry.push(entryitem);
            }
            var param = {
                simpleData: {
                    formId: 'stk_sostockreturn',
                    entry: JSON.stringify(checkEntry),
                    status: that.Model.getSimpleValue({ id: 'fstatus' }),
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/stk_sostockreturn?operationno=checkproducts', param, function (r) {
                var res = r.operationResult;
                e.result = true;
                var param = e.param;
                param.formId = "stk_sostockreturn";
                if (res.isSuccess && res.srvData.length > 0) {
                    yiDialog.c(res.srvData, function () {
                        that.Model.invokeFormOperation({
                            id: 'tbSave',
                            opcode: 'save',
                            param: param
                        });
                    });
                }
                else {
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: param
                    });
                }
            }, null, null, null, { async: false });
        };


        //标准定制
        _child.prototype.showstandardcustom = function () {
            ;
            var that = this;
            //选中行
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行标准定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('标准定制不支持多选！');
                return;
            };
            if (ds) {
                if (ds[0].data.fsourcebillno != '') {
                    yiDialog.warn('标准定制只能在无来源单时使用！');
                    return;
                }
                ////选中行
                //判断物料是否启用 选配类别 
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且未勾选非标定制
                if (Isenableselectioncategory && !fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框 
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或勾选了非标定制，不允许标准定制！');
                    return;
                }
            }
        };

        //非标定制
        _child.prototype.showunstandardcustom = function () {
            ;
            var that = this;
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行非标定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('非标定制不支持多选！');
                return;
            }
            if (ds) {
                if (ds[0].data.fsourcebillno != '') {
                    yiDialog.warn('非标准定制只能在无来源单时使用！');
                    return;
                }
                //判断选中行物料是否启用 选配类别
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //允许选配
                var fispresetprop = ds[0].data.fispresetprop;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且 勾选非标定制
                if (fispresetprop && fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或未勾选非标定制，不允许非标准定制！');
                    return;
                }
            }
        };

        //元素锁定控制
        _child.prototype.LoadElementEnable = function () {

            var that = this;
            var ftype = that.Model.getValue({ id: 'fsourcetype' });
            var fsourcenumber = that.Model.getValue({ id: "fsourcenumber" });
            if (fsourcenumber &&
                (ftype.fnumber != "stk_sostockout")) {//存在源单锁定 退货类型
                that.Model.setEnable({ id: 'freturntype', value: false });
            } else {
                that.Model.setEnable({ id: 'freturntype', value: true });
            }

            //存在源单隐藏锁定退款按钮
            //if (fsourcenumber) {
            //    that.Model.setVisible({ id: '[menu=Refund]', value: false });
            //    that.Model.setEnable({ id: '#tbRefund', value: false });
            //}
        }

        //初始化商品明细表的经销价插件
        _child.prototype.onBillInitProduct = function (param, params) {

            var that = this;
            var productData = [];

            if (param == 'init') {//初始化，


            } else if (param == 'change') {//改变某一行
                var reGridData = that.Model.getValue({ id: that.fentity });

                var rowData = {};
                for (var i = 0, l = reGridData.length; i < l; i++) {
                    if (reGridData[i] && reGridData[i].id == params.attrinfo.row) {
                        rowData = reGridData[i];
                    }
                }

                var reAttrinofEntry = [];//按照接口，重新组合
                var tempAttr = [];
                if (rowData.fattrinfo) {
                    tempAttr = rowData.fattrinfo.fentity;
                }

                for (var n = 0, m = tempAttr.length; n < m; n++) {
                    reAttrinofEntry.push({
                        valueId: tempAttr[n].fvalueid,
                        auxPropId: tempAttr[n].fauxpropid.id
                    })
                }

                productData.push({
                    clientId: rowData.id,
                    productId: rowData.fmaterialid.id,
                    bizDate: that.Model.uiData.fdate,
                    length: rowData.flength,
                    width: rowData.fwidth,
                    thick: rowData.fthick,
                    attrInfo: {
                        id: '',
                        entities: reAttrinofEntry
                    }
                });

            }

            productData = JSON.stringify(productData);

            that.Model.invokeFormOperation({
                id: param,

                opcode: 'getprices',
                //option: cvtParams,
                param: {
                    productInfos: productData,
                    formId: 'ydj_price',
                    domainType: 'dynamic'
                }
            });

        }

        //报价明细 辅助属性价格查询按钮点击事件
        _child.prototype.onPriceSerch = function (e) {
            var that = this;
            var flag = true;
            that.alertModel = e;
            var productData = [];
            var reAttrinofEntry = [];//按照接口，重新组合 
            var fentry = that.alertModel.Model.uiData.fentity;
            for (var n = 0, m = fentry.length; n < m; n++) {
                var lm = fentry[n];
                if (lm.fisselect) {//被选中的辅助属性行

                    if (!lm.fvalueid) {//辅助属性行需要填满信息才能查询
                        flag = false;
                    }
                    reAttrinofEntry.push({
                        valueId: lm.fvalueid,
                        auxPropId: lm.fauxpropid.id
                    })
                }

            }

            productData.push({
                clientId: '',
                productId: that.alertModel.Model.uiData.fmaterialid.id,
                bizDate: that.Model.uiData.fdate,//订单日期
                length: that.alertModel.Model.uiData.flength,
                width: that.alertModel.Model.uiData.fwidth,
                thick: that.alertModel.Model.uiData.fthick,
                attrInfo: {
                    id: '',
                    entities: reAttrinofEntry
                }
            });

            productData = JSON.stringify(productData);
            if (flag) {
                that.Model.invokeFormOperation({
                    id: 'onPriceSerch',

                    opcode: 'getprices',
                    //option: cvtParams,
                    param: {
                        productInfos: productData,
                        formId: 'ydj_price',
                        domainType: 'dynamic'
                    }
                });
            } else {
                yiDialog.mt({ msg: '辅助属性信息不全，无法查询价格。', skinseq: 2 });
            }


        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    //that.onBillInitProduct('change',{attrinfo:e});
                    break;
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    var isMain = that.Model.getValue({ id: 'fpublishstatus' });
                    //采购订单，发布状态  fpublishstatus=='publish_status_02' 的时候，全部锁住
                    if (isMain && isMain.id == 'publish_status_02') {
                        e.result = true;
                        yiDialog.mt({ msg: '已经建立协同关系，不允许删除商品明细！', skinseq: 2 });
                    }
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    //重新计算表头字段值
                    that.LoadAmount();

                    break;
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var isMain = that.Model.getValue({ id: 'fpublishstatus' });

            switch (e.id.toLowerCase()) {

                case 'fattrinfo':
                    //商品为空时，不允许编辑
                    productId = $.trim(that.Model.getSimpleValue({ id: 'fmaterialid', row: e.row }));
                    if (!productId) {
                        e.result.enabled = false;
                        return;
                    }
                    break;
            }
        };

        //获取基础资料字段动态过滤条件
        //      _child.prototype.onQueryFilterString = function (e) {
        //          if (!e.id) return;
        //          var that = this;
        //          //业务类型
        //          var billTypeId = '';
        //          switch (e.id.toLowerCase()) {
        //              //导购员
        //              case 'fstaffid':
        //                  var deptId = that.Model.getSimpleValue({ id: 'fdeptid' });
        //                  e.result.filterString = "fdeptid<>'' and fdeptid=@fdeptid";
        //                  e.result.params = [
        //                      { fieldId: 'fdeptid', pValue: deptId }
        //                  ];
        //                  break;
        //              //商品基础资料
        //              case 'fmaterialid':
        //                  billTypeId = $.trim(that.Model.getSimpleValue({ id: 'fbilltypeid' }));
        //                  if (billTypeId.toLowerCase() === 'po_type_02') {
        //                      //供应商ID
        //                      var supplierId = that.Model.getSimpleValue({ id: 'fsupplierid' });
        //                      e.result.filterString = "\
        //                          fcompanyid in (select fcoocompanyid from t_ydj_supplier where fid=@fsupplierid)";
        //                      e.result.params = [
        //                          { fieldId: 'fsupplierid', pValue: supplierId }
        //                      ];
        //                  }
        //                  break;
        //          }
        //      };

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {

                //数量单价的变化，影响金额
                case 'fqty':
                case 'fprice':
                    that.culNum({ name: e.id, rowId: e.row, value: e.value });
                    break;
                //计算总件数
                case 'fpackageqty':
                    that.culFbill();
                    break;
                //计算总立方数
                case 'fcubeqty':
                    that.culFbill();
                    break;
                case 'freturntype':
                    var fname = e.value.fname;
                    if (fname == "退货退款") {
                        that.Model.setEnable({ id: 'factualreturnamount', value: true });
                    } else {
                        that.Model.setEnable({ id: 'factualreturnamount', value: false });
                    }
                    that.LoadAmount();
                    break;
                case 'fmaterialid':
                    that.Model.setValue({ id: 'fattrinfo', row: e.row, value: { fentity: [] }, ctx: { tgFatchPrice: false } });
                    that.Model.setValue({ id: 'fattrinfo_e', row: e.row, value: { fentity: [] }, ctx: { tgFatchPrice: false } });
                    break;
            }
        };

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == that.fentity) {
                switch (e.btnid.toLowerCase()) {
                    case 'g_record': //批录按钮 
                        //仓位禁止批录，需求#35670 
                        if (e.fieldId.toLowerCase() === 'fstorehouseid') {
                            e.copyFields = ["fstockstatus"];
                            e.clearFields = ["fstorelocationid"];
                        }
                        break;
                }
            }
        };

        //重新计算明细
        _child.prototype.culFbill = function (e) {
            var that = this;
            var ds = that.Model.getEntryData({ id: that.fentity });
            var totalpackageQty = 0;
            var totalcubeQty = 0;
            if (ds && ds.length > 0) {
                for (var i = 0, l = ds.length; i < l; i++) {
                    totalpackageQty += yiMath.toNumber(ds[i].fpackageqty);
                    totalcubeQty += yiMath.toNumber(ds[i].fcubeqty);
                }
            }
            that.Model.setValue({ id: 'ftotalpackageqty', value: totalpackageQty });
            that.Model.setValue({ id: 'ftotalcubeqty', value: totalcubeQty });
        }

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case 'pack':
                    if (!isSuccess) {
                        that.Model.invokeFormOperation({
                            id: 'push',
                            opcode: 'push',
                            param: {
                                ruleid: "stk_sostockreturn2bcm_packorder"
                            }
                        });
                    }
                    break;
                case 'loadsettleinfo':
                    if (isSuccess && $.isPlainObject(srvData)) {
                        if (srvData.fsettletype === '退款') {
                            var title = e.result.operationResult.title;
                            var tagName = "订单编号";
                            if (title == "source_refund") {
                                tagName = "合同编号";
                            }
                            that.Model.showForm({
                                formId: 'coo_settledyn',
                                param: { openStyle: Consts.openStyle.modal },
                                cp: $.extend({}, srvData, {
                                    bizFields: [
                                        { id: 'fsourcenumber', cation: tagName },
                                        { id: 'fsettlemainname', cation: '客户' },
                                        { id: 'fsettledamount', cation: '确认已收' },
                                    ],
                                    fimage: { id: '', name: '' },
                                    callback: function (result) {
                                        if (result && result.isSuccess) {
                                            that.Model.refresh();
                                        }
                                    }
                                })
                            });
                        }
                    }
                    break;
                case 'pull':
                    if (isSuccess) {
                        //表体金额 = 表体数量 * 表体单价
                        var amount = 0;
                        var entry = srvData?.uidata?.fentity;
                        for (var i = 0; i < entry.length; i++) {
                            var qty = yiMath.toNumber(entry[i].fqty);
                            //单价
                            var price = yiMath.toNumber(entry[i].fprice);
                            amount += qty * price;
                        }

                        that.formContext.cp.type = 'pull'
                        var returntype = that.Model.getSimpleValue({ id: 'freturntype' });
                        if (returntype == "sostockreturn_biztype_02") {
                            that.Model.setEnable({ id: 'factualreturnamount', value: true });
                        } else {
                            that.Model.setEnable({ id: 'factualreturnamount', value: false });
                        }
                        //var actualreturnamount = that.Model.getValue({ id: 'factualreturnamount' });
                        //that.Model.setValue({ id: 'factualreturnamount', value: amount + yiMath.toNumber(actualreturnamount) });
                    }
                    break;
            }
        };

        _child.prototype.LoadAmount = function (opt) {
            var that = this;
            ;
            var type = that.Model.getValue({ id: 'freturntype' });
            if (!(type && type.id == "sostockreturn_biztype_02")) {
                that.Model.setValue({ id: 'fplanreturnamount', value: 0 });
                return;
            }

            //行对象
            var row = that.Model.getEntryData({ id: that.fentity });

            if (!row) {

                return;
            }
            //表体金额 = 表体数量 * 表体单价
            var amount = 0;
            for (var i = 0; i < row.length; i++) {
                var qty = yiMath.toNumber(row[i].fqty);
                //单价
                var price = yiMath.toNumber(row[i].fprice);
                amount += qty * price;
            }
            that.Model.setValue({ id: 'fplanreturnamount', value: amount });

            if (that.formContext && that.formContext.cp && that.formContext.cp.type && that.formContext.cp.type == 'pull') {
                that.Model.setValue({ id: 'factualreturnamount', value: amount });
                var returntype = that.Model.getSimpleValue({ id: 'freturntype' });
                if (returntype == "sostockreturn_biztype_02") {
                    that.Model.setEnable({ id: 'factualreturnamount', value: true });
                } else {
                    that.Model.setEnable({ id: 'factualreturnamount', value: false });
                }

                that.formContext.cp.type = "";
            }
        };

        _child.prototype.culNum = function (opt) {
            var that = this;
            //行对象
            var row = that.Model.getEntryRowData({ id: that.fentity, row: opt.rowId });

            if (!row) {

                return;
            }
            //数量
            var qty = yiMath.toNumber(row.fqty);
            //单价
            var price = yiMath.toNumber(row.fprice);
            //表体金额 = 表体数量 * 表体单价
            var amount = qty * price;
            //金额设值
            that.Model.setValue({ id: 'famount', row: opt.rowId, value: amount });
            that.LoadAmount();
        };


        //设置一件代发显示隐藏
        _child.prototype.setDropShipMentVisible = function () {
            var that = this;
            var flag = false;
            var billtypename = that.Model.getValue({ id: "fbilltype" })?.fname;
            if ((billtypename == "v6定制柜合同" || billtypename == "v6定制柜合同")) {
                flag = false;
            } else {
                var parm = {
                };
                yiAjax.p('/bill/ydj_order?operationno=getagentcustomchannel', parm, function (r) {
                    if (r) {
                        if (r.operationResult?.srvData) {
                            var fcustomchannel = r.operationResult.srvData["fcustomchannel"];
                            if (fcustomchannel == "1") {
                                flag = true;
                            }
                            var ftoppiecesendtag = r.operationResult.srvData["ftoppiecesendtag"];
                            var fmanagemodel = r.operationResult.srvData["fmanagemodel"];
                            if (fmanagemodel == 1 && ftoppiecesendtag) {
                                flag = true;
                            } else if (fmanagemodel == 1 && !ftoppiecesendtag) {
                                flag = false;
                            }
                        }
                    }
                }, null, null, null, { async: false });
            }
            var fpiecesendtag = that.Model.getValue({ id: 'fpiecesendtag' });
            var fbacktohq = that.Model.getValue({ id: 'fbacktohq' });
            if (fpiecesendtag || fbacktohq) {
                that.Model.setEnable({ id: 'fstorehouseid', value: false });//不允许选择仓库
            } else {
                that.Model.setEnable({ id: 'fstorehouseid', value: true });//不允许选择仓库
            }
            that.Model.setVisible({ id: '.dropshipping-info-tag', value: flag });//一件代发相关字段

            var visible = false;
            if (Consts.isdirectsale) {
                visible = true;
            } else {
                visible = false;
            }

            that.Model.setVisible({ id: 'fprice', value: visible });//零售价
            that.Model.setVisible({ id: 'factualrefundamount', value: visible });//实退金额
            that.Model.setVisible({ id: 'frefundrate', value: visible });//退款折率
            that.Model.setVisible({ id: 'frefundrateamount', value: visible });//退款总折扣额
            that.Model.setVisible({ id: 'frefundrateprice', value: visible });//退款折扣额
            that.Model.setVisible({ id: 'frefundtail', value: visible });//直营退款尾差
            that.Model.setVisible({ id: 'fcommissionproportion', value: visible });//抽佣比例
            that.Model.setVisible({ id: 'fcommissionamount', value: visible });//抽佣金额
        }

        return _child;
    })(BasePlugIn);
    window.stk_sostockreturn = window.stk_sostockreturn || stk_sostockreturn;
})();