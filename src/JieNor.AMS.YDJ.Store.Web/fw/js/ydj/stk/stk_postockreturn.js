/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/stk/stk_postockreturn.js
 */
; (function () {
    var stk_postockreturn = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.filesavemsg = [];//全局存储信息
            that.result = [];
            that.tempId = [];
            that.succArr = [];
            that.nowRow = '';//当前被双击的行
            that.suentry = [];
            that.remindnumbers = "";
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';//商品信息

        //初始化动态表单插件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            var returntype = that.Model.getSimpleValue({ id: 'freturntype' });
            if (returntype == "postockreturn_biztype_02") {
                that.Model.setEnable({ id: 'factualreturnamount', value: true });
            } else {
                that.Model.setEnable({ id: 'factualreturnamount', value: false });
            }

        };

        //页面视图初始化后事件
        _child.prototype.onViewInitialized = function (args) {
            var that = this;
            var returntype = that.Model.getSimpleValue({ id: 'freturntype' });
            if (returntype == "postockreturn_biztype_02") {
                that.Model.setEnable({ id: 'factualreturnamount', value: true });
            } else {
                that.Model.setEnable({ id: 'factualreturnamount', value: false });
            }
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.fentity:

                    break;
            }
        };

        _child.prototype.onBillInitialized = function (args) {
            
            var that = this;
            that.LoadElementEnable();
            var fstatus = that.Model.getSimpleValue({ id: 'fstatus' });
            //如果是提交、审核不走以下逻辑。
            if (that.formContext && that.formContext.cp && that.formContext.cp.type && that.formContext.cp.type == 'pull') {
                that.LoadAmount();
            }
            that.FieldLockCheck();


        };

        //元素锁定控制
        _child.prototype.LoadElementEnable = function () {

            var that = this;
            var ftype = that.Model.getValue({ id: 'fsourcetype' });
            var fsourcenumber = that.Model.getValue({ id: "fsourcenumber" });
            if (fsourcenumber &&
                (ftype.fnumber != "stk_postockin")) {//存在源单锁定 退货类型
                that.Model.setEnable({ id: 'freturntype', value: false });
            } else {
                that.Model.setEnable({ id: 'freturntype', value: true });
            }

            var fstatus = that.Model.getValue({ id: 'fstatus' });
            var fcancelstatus = that.Model.getValue({ id: "fcancelstatus" });
            if (fstatus.id == 'D' && fcancelstatus == false) {
                that.Model.setEnable({ id: '#tbDeliveryTask', value: true });
            }
            else {
                that.Model.setEnable({ id: '#tbDeliveryTask', value: false });
            }
        }


        //初始化商品明细表的经销价插件
        _child.prototype.onBillInitProduct = function (param, params) {

            var that = this;
            var productData = [];

            if (param == 'init') {//初始化，


            } else if (param == 'change') {//改变某一行
                var reGridData = that.Model.getValue({ id: that.fentity });

                var rowData = {};
                for (var i = 0, l = reGridData.length; i < l; i++) {
                    if (reGridData[i] && reGridData[i].id == params.attrinfo.row) {
                        rowData = reGridData[i];
                    }
                }

                var reAttrinofEntry = [];//按照接口，重新组合
                var tempAttr = [];
                if (rowData.fattrinfo) {
                    tempAttr = rowData.fattrinfo.fentity;
                }

                for (var n = 0, m = tempAttr.length; n < m; n++) {
                    reAttrinofEntry.push({
                        valueId: tempAttr[n].fvalueid,
                        auxPropId: tempAttr[n].fauxpropid.id
                    })
                }

                productData.push({
                    clientId: rowData.id,
                    productId: rowData.fmaterialid.id,
                    bizDate: that.Model.uiData.fdate,
                    length: rowData.flength,
                    width: rowData.fwidth,
                    thick: rowData.fthick,
                    attrInfo: {
                        id: '',
                        entities: reAttrinofEntry
                    }
                });
            }

            productData = JSON.stringify(productData);

            that.Model.invokeFormOperation({
                id: param,

                opcode: 'getprices',
                //option: cvtParams,
                param: {
                    productInfos: productData,
                    formId: 'ydj_price',
                    domainType: 'dynamic'
                }
            });
        }

        //报价明细 辅助属性价格查询按钮点击事件
        _child.prototype.onPriceSerch = function (e) {
            var that = this;
            var flag = true;
            that.alertModel = e;
            var productData = [];
            var reAttrinofEntry = [];//按照接口，重新组合 
            var fentry = that.alertModel.Model.uiData.fentity;
            for (var n = 0, m = fentry.length; n < m; n++) {
                var lm = fentry[n];
                if (lm.fisselect) {//被选中的辅助属性行

                    if (!lm.fvalueid) {//辅助属性行需要填满信息才能查询
                        flag = false;
                    }
                    reAttrinofEntry.push({
                        valueId: lm.fvalueid,
                        auxPropId: lm.fauxpropid.id
                    })
                }

            }

            productData.push({
                clientId: '',
                productId: that.alertModel.Model.uiData.fmaterialid.id,
                bizDate: that.Model.uiData.fdate,//订单日期
                length: that.alertModel.Model.uiData.flength,
                width: that.alertModel.Model.uiData.fwidth,
                thick: that.alertModel.Model.uiData.fthick,
                attrInfo: {
                    id: '',
                    entities: reAttrinofEntry
                }
            });

            productData = JSON.stringify(productData);
            if (flag) {
                that.Model.invokeFormOperation({
                    id: 'onPriceSerch',

                    opcode: 'getprices',
                    //option: cvtParams,
                    param: {
                        productInfos: productData,
                        formId: 'ydj_price',
                        domainType: 'dynamic'
                    }
                });
            } else {
                yiDialog.mt({ msg: '辅助属性信息不全，无法查询价格。', skinseq: 2 });
            }


        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    //that.onBillInitProduct('change',{attrinfo:e});
                    break;
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    var isMain = that.Model.getValue({ id: 'fpublishstatus' });
                    //采购订单，发布状态  fpublishstatus=='publish_status_02' 的时候，全部锁住
                    if (isMain && isMain.id == 'publish_status_02') {
                        e.result = true;
                        yiDialog.mt({ msg: '已经建立协同关系，不允许删除商品明细！', skinseq: 2 });
                    }
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    //重新计算表头字段值
                    that.LoadAmount();

                    break;
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var isMain = that.Model.getValue({ id: 'fpublishstatus' });

            switch (e.id.toLowerCase()) {

                case 'fattrinfo':
                    //商品为空时，不允许编辑
                    productId = $.trim(that.Model.getSimpleValue({ id: 'fmaterialid', row: e.row }));
                    if (!productId) {
                        e.result.enabled = false;
                        return;
                    }
                    break;
            }
        };

        //获取基础资料字段动态过滤条件
        //      _child.prototype.onQueryFilterString = function (e) {
        //          if (!e.id) return;
        //          var that = this;
        //          //业务类型
        //          var billTypeId = '';
        //          switch (e.id.toLowerCase()) {
        //              //导购员
        //              case 'fstaffid':
        //                  var deptId = that.Model.getSimpleValue({ id: 'fdeptid' });
        //                  e.result.filterString = "fdeptid<>'' and fdeptid=@fdeptid";
        //                  e.result.params = [
        //                      { fieldId: 'fdeptid', pValue: deptId }
        //                  ];
        //                  break;
        //              //商品基础资料
        //              case 'fmaterialid':
        //                  billTypeId = $.trim(that.Model.getSimpleValue({ id: 'fbilltypeid' }));
        //                  if (billTypeId.toLowerCase() === 'po_type_02') {
        //                      //供应商ID
        //                      var supplierId = that.Model.getSimpleValue({ id: 'fsupplierid' });
        //                      e.result.filterString = "\
        //                          fcompanyid in (select fcoocompanyid from t_ydj_supplier where fid=@fsupplierid)";
        //                      e.result.params = [
        //                          { fieldId: 'fsupplierid', pValue: supplierId }
        //                      ];
        //                  }
        //                  break;
        //          }
        //      };

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {

                //数量单价的变化，影响金额
                case 'fqty':
                case 'fprice':
                    that.culNum({ name: e.id, rowId: e.row, value: e.value });
                    break;
                case 'freturntype':
                    var fname = e.value.fname;
                    if (fname == "退货退款") {
                        that.Model.setEnable({ id: 'factualreturnamount', value: true });
                    } else {
                        that.Model.setEnable({ id: 'factualreturnamount', value: false });
                    }
                    that.LoadAmount();
                    break;
                case 'fbilltype':
                    that.FieldLockCheck();
                    break;
                case 'fmaterialid':
                    that.Model.setValue({ id: 'fattrinfo', row: e.row, value: { fentity: [] }, ctx: { tgFatchPrice: false } });
                    that.Model.setValue({ id: 'fattrinfo_e', row: e.row, value: { fentity: [] }, ctx: { tgFatchPrice: false } });
                    break;
            }
        };

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == that.fentity) {
                switch (e.btnid.toLowerCase()) {
                    case 'g_record': //批录按钮 
                        //仓位禁止批录，需求#35670 
                        if (e.fieldId.toLowerCase() === 'fstorehouseid') {
                            e.copyFields = ["fstockstatus"];
                            e.clearFields = ["fstorelocationid"];
                        }
                        break;
                }
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                //case 'isexsitbarcode':
                //    //if (!isSuccess) {
                //    //    yiDialog.warn("当前商品未查询到对应条码信息!");
                //    //    return false;
                //    //}
                //    //弹出《条码主档》列表界面
                //    var filterString = "fmainorgid = '{0}' and fid in ({1})"
                //        .format(Consts.loginCompany.id, srvData.split(','));
                //    that.Model.showForm({
                //        formId: 'bcm_barcodemaster',
                //        domainType: Consts.domainType.list,
                //        param: {
                //            openStyle: Consts.openStyle.modal,
                //            filterstring: filterString
                //        }
                //    });
                //    break;
                case "querylock":
                    if (!isSuccess || !srvData) {
                        return false;
                    }
                    var enable = true;
                    for (var i = 0; i < srvData.length; i++) {
                        if (srvData[i].flock == '-1') {
                            //全部锁定
                            enable = false;

                        } else {
                            if ($.trim(that.Model.pkid)) {
                                //修改锁定
                                if (srvData[i].flock == '2') {
                                    enable = false;
                                }
                            } else {
                                //新增锁定
                                if (srvData[i].flock == '1') {
                                    enable = false;
                                } else {
                                    enable = false;
                                }
                            }
                        }
                        that.Model.setEnable({ id: srvData[i].ffieldid, value: enable });
                    }
                    break;
                case 'pull':
                    if (isSuccess) {
                        //表体金额 = 表体数量 * 表体单价
                        var amount = 0;
                        var entry = srvData?.uidata?.fentity;
                        for (var i = 0; i < entry.length; i++) {
                            var qty = yiMath.toNumber(entry[i].fqty);
                            //单价
                            var price = yiMath.toNumber(entry[i].fprice);
                            amount += qty * price;
                        }

                        that.formContext.cp.type = 'pull'
                        var returntype = that.Model.getSimpleValue({ id: 'freturntype' });
                        if (returntype == "postockreturn_biztype_02") {
                            that.Model.setEnable({ id: 'factualreturnamount', value: true });
                        } else {
                            that.Model.setEnable({ id: 'factualreturnamount', value: false });
                        }
                        //var actualreturnamount = that.Model.getValue({ id: 'factualreturnamount' });
                        //that.Model.setValue({ id: 'factualreturnamount', value: amount + yiMath.toNumber(actualreturnamount) });
                    }
                    break;
            }
        };

        _child.prototype.LoadAmount = function (opt) {
            var that = this;
            ;
            var type = that.Model.getValue({ id: 'freturntype' });
            if (!(type && type.id == "postockreturn_biztype_02")) {
                that.Model.setValue({ id: 'fplanreturnamount', value: 0 });
                return;
            }

            //行对象
            var row = that.Model.getEntryData({ id: that.fentity });

            if (!row) {

                return;
            }
            //表体金额 = 表体数量 * 表体单价
            var amount = 0;
            for (var i = 0; i < row.length; i++) {
                var qty = yiMath.toNumber(row[i].fqty);
                //单价
                var price = yiMath.toNumber(row[i].fprice);
                amount += qty * price;
            }
            that.Model.setValue({ id: 'fplanreturnamount', value: amount });

            if (that.formContext && that.formContext.cp && that.formContext.cp.type && that.formContext.cp.type == 'pull') {
                that.Model.setValue({ id: 'factualreturnamount', value: amount });
                var returntype = that.Model.getSimpleValue({ id: 'freturntype' });
                if (returntype == "postockreturn_biztype_02") {
                    that.Model.setEnable({ id: 'factualreturnamount', value: true });
                } else {
                    that.Model.setEnable({ id: 'factualreturnamount', value: false });
                }

                that.formContext.cp.type = "";
            }
        };

        _child.prototype.culNum = function (opt) {
            var that = this;
            //行对象
            var row = that.Model.getEntryRowData({ id: that.fentity, row: opt.rowId });

            if (!row) {

                return;
            }
            //数量
            var qty = yiMath.toNumber(row.fqty);
            //单价
            var price = yiMath.toNumber(row.fprice);
            //表体金额 = 表体数量 * 表体单价
            var amount = qty * price;
            //金额设值
            that.Model.setValue({ id: 'famount', row: opt.rowId, value: amount });

            var poprice = yiMath.toNumber(row.fpoprice);
            var poamount = qty * poprice;
            //金额设值
            that.Model.setValue({ id: 'fpoamount', row: opt.rowId, value: poamount });

            that.LoadAmount();
        };

        _child.prototype.FieldLockCheck = function () {

            ;

            var that = this;

            var fbilltype = that.Model.getValue({ id: 'fbilltype' });

            if (fbilltype != undefined && fbilltype.id != undefined && fbilltype.id == 'postockreturn_billtype_02') {
                that.Model.setEnable({ id: 'fprice', value: true });
                that.Model.setEnable({ id: 'famount', value: true });
                that.Model.setEnable({ id: 'fpoprice', value: true });
                that.Model.setEnable({ id: 'fpoamount', value: true });
            }
            else {
                that.Model.setEnable({ id: 'fprice', value: false });
                that.Model.setEnable({ id: 'famount', value: false });
                that.Model.setEnable({ id: 'fpoprice', value: false });
                that.Model.setEnable({ id: 'fpoamount', value: false });
            }


        };

        //行选中事件
        _child.prototype.onSelectedRowsChanged = function (e) {
            var that = this;
            if (!e.id) return;

            switch (e.id.toLowerCase()) {
                case "list":
                    if (!e.data && e.data.length <= 0) return;

                    var filterdata = e.data.filter(x => x.fstatus == 'D' && x.fcancelstatus == '0');
                    if (filterdata.length > 0 && filterdata.length == e.data.length) {
                        that.Model.setEnable({ id: '#tbDeliveryTask', value: true });
                    }
                    else {
                        that.Model.setEnable({ id: '#tbDeliveryTask', value: false });
                    }
                    break;
            }
        };

        // 选单
        _child.prototype.pull = function (e) {
            var that = this;
            var sourceNumber = that.Model.getValue({ id: 'fsourcenumber' });
            if (sourceNumber) {
                e.param.sourceNumber = sourceNumber;
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'pull':
                    that.pull(e);
                    break;
                case 'querybarcode'://条码联查
                    that.querybarcode(e);
                    break;
                case 'rejectflow':
                case 'saveaudit':
                case 'auditflow':
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    if (checkorder && that.remindnumbers) {
                        e.result = true;
                        yiDialog.d({
                            id: 'remindnumbers',
                            type: 1,
                            resize: false,
                            maxmin: false,
                            title: '系统提示',
                            content: that.remindnumbers,
                            area: ['400px', '200px'],
                            btn: ['确定'],
                            yes: function (index, layero) {
                                layer.close(index);
                            }
                        });
                    }
                    break;
            }
        };

        //检查订单是否满足条件
        _child.prototype.checkOrder = function (opname) {
            var that = this;
            var isremind = false;

            var selectedRows;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                selectedRows = [{ pkValue: that.Model.pkid }];
            }
            if (that.Model.viewModel.domainType == Consts.domainType.list) {
                selectedRows = that.Model.getSelectRows();
                if (!selectedRows || selectedRows.length <= 0) {
                    yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                    return;
                }
            }

            var ids = [];
            for (var i = 0; i < selectedRows.length; i++) {
                ids.push(selectedRows[i].pkValue);
            }
            var param = {
                simpleData: {
                    formId: 'stk_postockreturn',
                    Ids: ids.join(","),
                    opname: opname
                }
            };

            yiAjax.p('/bill/stk_postockreturn?operationno=verifyproduct', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (res.isSuccess && srvData) {
                    isremind = true;
                    that.remindnumbers = srvData;
                }
            }, null, null, null, { async: false });
            return isremind;
        };

        //条码联查
        _child.prototype.querybarcode = function (e) {
            var that = this;
            e.result = true;

            var selRows = that.Model.getSelectRows({ id: that.fentity });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('请先选中行再操作!');
                return;
            }
            //if (selRows.length > 1) {
            //    yiDialog.warn('只允许勾选一行进行条码联查!');
            //    return;
            //}
            var datas = [];
            for (var i = 0; i < selRows.length; i++) {
                datas.push({ seldata: selRows[i].data.fmaterialid.id });
            }
            //JSON.stringify(datas)
            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            that.Model.invokeFormOperation({
                id: 'isexsitbarcode',
                opcode: 'isexsitbarcode',
                param: {
                    'formId': 'bcm_barcodemaster',
                    'fsourcetype': 'stk_postockreturn',
                    'fsourcenumber': fbillno,
                    'fmaterialid': JSON.stringify(datas),
                }
            });
        };
        //表格行创建后事件
        _child.prototype.onEntryRowCreated = function (e) {
            var that = this;
            that.QueryLock();
        }

        _child.prototype.QueryLock = function (opt) {
            var that = this;
            var fbilltype = that.Model.getValue({ id: 'fbilltype' });
            if (fbilltype) {
                that.Model.invokeFormOperation({
                    id: 'querylock',
                    opcode: 'querylock',
                    param: {
                        'fid': fbilltype.id,
                        'ffieldid': "'fprice','famount','fpoprice','fpoamount'",
                    }
                });
            }
        };

        return _child;
    })(BasePlugIn);
    window.stk_postockreturn = window.stk_postockreturn || stk_postockreturn;
})();