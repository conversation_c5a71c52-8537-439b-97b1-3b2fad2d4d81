using System;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataManager;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Service
{
    /// <summary>
    /// 门店服务实现
    /// </summary>
    [InjectService]
    public class StoreService : IStoreService
    {
        /// <summary>
        /// 获取门店对应的部门
        /// </summary>
        /// <param name="agentCtx">经销商上下文</param>
        /// <param name="storeNos">门店编码</param>
        /// <returns></returns>
        public List<DynamicObject> GetDeptByStoreNo(UserContext agentCtx, IEnumerable<string> storeNos)
        {
            if (storeNos.IsNullOrEmpty()) return new List<DynamicObject>();

            var depts = agentCtx.LoadBizDataByNo("ydj_dept", "fstoreid", storeNos);//部门上记录有门店编码，通过这个来校验是否存在对应的部门

            return depts;
        }

        ///// <summary>
        ///// 反写部门信息（如果已经存在对应的部门, 则不要更新部门名称 与 部门编码）
        ///// </summary>
        ///// <param name="agentCtx">经销商上下文</param>
        ///// <param name="stores">门店</param>
        ///// <param name="depts">部门</param>
        //public void RewriteDept(UserContext agentCtx, IEnumerable<DynamicObject> stores, IEnumerable<DynamicObject> depts)
        //{
        //    if (stores.IsNullOrEmpty() || depts.IsNullOrEmpty()) return;

        //    foreach (var store in stores)
        //    {
        //        var number = Convert.ToString(store["fnumber"]);
        //        // 保存的是门店编码
        //        var ds = depts.Where(s => Convert.ToString(s["fstoreid"]).EqualsIgnoreCase(number));
        //        foreach (var dept in ds)
        //        {
        //            // 如果《门店》有【门店简称】时, 默认以【门店简称】创建 与 更新《部门》的【名称】, 没有则用《门店》的【门店名称】更新《部门》的【名称】
        //            dept["fname"] = store["fname"].IsNullOrEmptyOrWhiteSpace() ? store["fname"] : store["fshortname"];
        //            dept["fshortname"] = store["fshortname"];
        //        }
        //    }

        //    agentCtx.SaveBizData("ydj_dept", depts);
        //}

        /// <summary>
        /// 更新【单据头.经销商名称】和【单据头. 送达方】
        /// 注：需要自行保存
        /// </summary>
        /// <param name="topCtx">总部上下文</param>
        /// <param name="stores">门店</param>
        public void UpdateAgentAndDeliver(UserContext topCtx, IEnumerable<DynamicObject> stores)
        {
            stores = stores?
                .Where(s => s["fsrcstoreid"].IsNullOrEmptyOrWhiteSpace());
            if (stores.IsNullOrEmpty()) return;

            /*
             * 按照 《门店》上的”城市+实控人”来生赋值【经销商】 (根据《门店》的【城市】+【实控人ID】找到《送达方》, 获取《送达方》上的《经销商》) , 但如果有配置表就按照《主经销商配置表》里的”主经销商”来生成 (也就是如果《送达方》上的《经销商》, 能够在 没被禁用的《主经销商配置表》里匹配上表体的【子经销商】, 则默认获取该配置表的 表头【主经销商】作为《门店》的【经销商】)
             *
             * 根据【城市】+【实控人ID】找到《送达方》, 将找到的《送达方》单据头【名称】用逗号串联成文本进行赋值
             */

            Dictionary<string, Tuple<string, string>> dic = new Dictionary<string, Tuple<string, string>>();

            foreach (var store in stores)
            {
                string key = $"{store["fmycity"]}_{store["actualownernumber"]}";
                if (!dic.ContainsKey(key))
                {
                    dic[key] = Tuple.Create(Convert.ToString(store["fmycity"]),
                        Convert.ToString(store["actualownernumber"]));
                }
            }

            var sqls = new List<string>();

            foreach (var item in dic)
            {
                var cityId = item.Value.Item1;
                var actualOwnerNumber = item.Value.Item2;

                if (cityId.IsNullOrEmptyOrWhiteSpace() && actualOwnerNumber.IsNullOrEmptyOrWhiteSpace()) continue;

                var sql = $@"
select fname, fcity, actualownernumber, fagentid from t_bas_deliver with(nolock) where fcity='{cityId}' and actualownernumber='{actualOwnerNumber}' and fforbidstatus='0'
";

                sqls.Add(sql);
            }

            List<DynamicObject> delivers = new List<DynamicObject>();

            if (sqls.Any())
            {
                string unionSql = string.Join(" union all ", sqls);
                var dbService = topCtx.Container.GetService<IDBService>();
                delivers = dbService.ExecuteDynamicObject(topCtx, unionSql).ToList();
            }

            var bizAgentIds = topCtx.Container.GetService<IAgentService>()
                .GetBizAgentIdByIds(topCtx, delivers.Select(s => Convert.ToString(s["fagentid"])));

            foreach (var store in stores)
            {
                var fmycity = Convert.ToString(store["fmycity"]);
                var actualownernumber = Convert.ToString(store["actualownernumber"]);

                if (fmycity.IsNullOrEmptyOrWhiteSpace() && actualownernumber.IsNullOrEmptyOrWhiteSpace()) continue;

                var ds = delivers.Where(s =>
                    Convert.ToString(s["fcity"]).EqualsIgnoreCase(fmycity) && Convert.ToString(s["actualownernumber"])
                        .EqualsIgnoreCase(actualownernumber)).ToList();

                var agentId = string.Empty;
                //新逻辑：当【实控人+城市】匹配到多个送达方且送达方对应的经销商不一样时，需要取《门店与系列》中的外部售达方id 
                var ms_ag = ds.Where(s => !Convert.ToString(s["fagentid"]).IsNullOrEmptyOrWhiteSpace()).Select(s => Convert.ToString(s["fagentid"])).Distinct();
                if (ms_ag.Count() > 1)
                {
                    var ftranid = Convert.ToString(store["ftranid"]);
                    agentId = GetAgentObjByMS_STORE(topCtx, ftranid);
                    //如果门店与系列中匹配不到则再取门店关联经销商
                    if (agentId.IsNullOrEmptyOrWhiteSpace())
                    {
                        agentId =
                       Convert.ToString(
                           ds.FirstOrDefault(s => !Convert.ToString(s["fagentid"]).IsNullOrEmptyOrWhiteSpace())?[
                               "fagentid"]);
                    }
                }
                else
                {
                    agentId =
                    Convert.ToString(
                        ds.FirstOrDefault(s => !Convert.ToString(s["fagentid"]).IsNullOrEmptyOrWhiteSpace())?[
                            "fagentid"]);
                }


                // 根据【城市】+【实控人ID】找到《送达方》, 将找到的《送达方》单据头【名称】用逗号串联成文本进行赋值
                store["fsellername"] = string.Join(",", ds.Select(s => Convert.ToString(s["fname"])));

                if (agentId.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                if (bizAgentIds.TryGetValue(agentId, out var bizAgentId))
                {
                    store["fagentid"] = bizAgentId;
                    store["forgid"] = bizAgentId;   // 根据【经销商】所对应的《组织》赋值
                }
            }
        }

        private string GetAgentObjByMS_STORE(UserContext topCtx, string storeid)
        {
            var agentid = string.Empty;
            var sql = $@"
                        select distinct ag.fid from t_ms_store_series as ms_st with (nolock)
                        inner join t_bas_agent as ag with (nolock) on ms_st.fagentid = ag.ftranid
                        where fstoreid ='{storeid}' ";
            var dbService = topCtx.Container.GetService<IDBService>();
            agentid = dbService.ExecuteDynamicObject(topCtx, sql).Where(o => !Convert.ToString(o["fid"]).IsNullOrEmptyOrWhiteSpace()).Select(o => Convert.ToString(o["fid"]))?.FirstOrDefault();
            return agentid;

        }

        /// <summary>
        /// 更新【单据头.招商经销商】
        /// 注：需要自行保存
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="stores"></param>
        public void UpdateCrmDistributor(UserContext topCtx, IEnumerable<DynamicObject> stores)
        {
            stores = stores?
                .Where(s => s["fsrcstoreid"].IsNullOrEmptyOrWhiteSpace());
            if (stores.IsNullOrEmpty()) return;

            // 通过 接口清单的 19、门店   里面根据对应经销商ID, 带出对应基础资料的【招商经销商】

            var foutcrmdistributorids = stores.Select(s => Convert.ToString(s["foutcrmdistributorid"]))
                .Where(s => s.IsNullOrEmptyOrWhiteSpace() == false).ToList();

            // 清空字段
            //BUG 31650 业务维护了招商经销商 这里又被清空了，所以要先注释此逻辑，找到就更新 找不到就跳过。
            //if (foutcrmdistributorids.IsNullOrEmpty())
            //{
            //    foreach (var store in stores)
            //    {
            //        store["fcrmdistributorid"] = "";
            //    }

            //    return;
            //}

            // 排除禁用状态的
            var sql = $"select fid from t_ms_crmdistributor with(nolock) where fforbidstatus='0' and fid in ({foutcrmdistributorids.JoinEx(",", true)})";

            var dbService = topCtx.Container.GetService<IDBService>();
            var crmDistributors = dbService.ExecuteDynamicObject(topCtx, sql);

            foreach (var store in stores)
            {
                var foutcrmdistributorid = store["foutcrmdistributorid"]?.ToString();

                if (foutcrmdistributorid.IsNullOrEmptyOrWhiteSpace()) continue;

                var crmDistributor = crmDistributors.FirstOrDefault(s => Convert.ToString(s["fid"]).EqualsIgnoreCase(foutcrmdistributorid));

                store["fcrmdistributorid"] = Convert.ToString(crmDistributor?["fid"]);
            }
        }

        /// <summary>
        /// 门店上级组织更新时同步更新组织的上级组织 禅道BUG：31064
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="stores"></param>
        public void UpdataTopOrg(UserContext userCtx, IEnumerable<DynamicObject> stores)
        {
            if (stores.IsNullOrEmpty())
            {
                return;
            }

            var storeIds = stores.Select(s => Convert.ToString(s["id"]));

            // 【门店】对应的【组织】id相同
            var orgs = userCtx.LoadBizDataById("bas_organization", storeIds);

            var beUpdate = new List<DynamicObject>();

            foreach (var org in orgs)
            {
                var orgId = Convert.ToString(org["id"]);
                var store = stores.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(orgId));
                if (store == null)
                {
                    continue;
                }

                var fparentid = Convert.ToString(org["fparentid"]);
                var forgid = Convert.ToString(store["forgid"]);
                if (fparentid.EqualsIgnoreCase(forgid))
                {
                    continue;
                }

                //同步组织的上级组织为对应门店的上级组织。
                org["fparentid"] = store["forgid"];

                beUpdate.Add(org);
            }

            if (beUpdate.Any())
            {
                var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                var result = gateway.InvokeBillOperation(userCtx, "bas_organization", beUpdate, "save",
                    new Dictionary<string, object>
                    {
                        { "IsReturnBillUniqueValidationDetailErrorMessage", true }
                    });
                result.ThrowIfHasError(true, "组织保存失败！");
            }
        }

        /// <summary>
        /// 生成商品授权清单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="stores"></param>
        public void AddOrUpdateProductAuth(UserContext userCtx, IEnumerable<DynamicObject> stores)
        {
            stores = stores?
                .Where(s => s["fsrcstoreid"].IsNullOrEmptyOrWhiteSpace());
            if (stores.IsNullOrEmpty()) return;

            /*
             * 金蝶主动获取 (中台接口里有的就获取新增或更新), 根据【授予组织】来进行新增或覆盖, 并且覆盖时只会针对”单据体-按【品牌/系列】授权”的数据进行增减(每次都是全部替换), 不能影响到其他的单据体(如: 单据体-按【商品】授权 或 单据体-例外商品)
             */

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var dbService = userCtx.Container.GetService<IDBService>();
            var productAuthMeta = metaModelService.LoadFormModel(userCtx, "ydj_productauth");
            var productAuthDt = productAuthMeta.GetDynamicObjectType(userCtx);

            var externalIds = stores.Select(s => Convert.ToString(s["ftranid"])).Distinct().ToList();
            var storeIds = stores.Select(s => Convert.ToString(s["id"])).Distinct().ToList();

            // 【门店】对应的【组织】id相同
            var orgs = userCtx.LoadBizDataById("bas_organization", storeIds);

            // 获取已有的《商品授权清单》
            var productAuths = userCtx.LoadBizDataByNo("ydj_productauth", "forgid", storeIds);
            // 需要保存的《商品授权清单》
            List<DynamicObject> savingProductAuths = new List<DynamicObject>();

            // 获取《门店与系列》
            // 扩展获取父级系列（即业绩品牌）
            string sql = $@"
select ss.fstoreid, ss.fmodifydate, s.fid as fserieid, s.fname as fseriename, s.fbrandid,  p.fid as fparentserieid,p.fname as fparentseriename, ss.fforbidstatus
,isnull(bd.fauto_M1,0) as fauto_M1 ,ISNULL(bd.fauto_Z1,0) as fauto_Z1 ,ISNULL(bd.fmusibrand,0) as fmusibrand from t_ms_store_series ss with(nolock) 
inner join t_ydj_series s with(nolock) on ss.fseriesnumber = s.fnumber
left join t_ydj_series p with(nolock) on s.fparentid=p.fid
left join t_ydj_brand bd with(nolock) on bd.fid = s.fbrandid
where ss.fstoreid in ('{string.Join("','", externalIds)}')
";
            var storeSerieses = dbService.ExecuteDynamicObject(userCtx, sql).ToList();
            storeSerieses = DistinctStoreSerieses(storeSerieses);

            // 生成或更新《商品授权清单》
            foreach (var org in orgs)
            {
                var orgId = Convert.ToString(org["id"]);
                var store = stores.First(s => Convert.ToString(s["id"]).EqualsIgnoreCase(orgId));
                // 门店外部id
                var storeExtenalId = Convert.ToString(store["ftranid"]);
                var productAuth =
                    productAuths.FirstOrDefault(s => Convert.ToString(s["forgid"]).EqualsIgnoreCase(orgId));
                if (productAuth == null)
                {
                    productAuth = (DynamicObject)productAuthDt.CreateInstance();
                }

#warning 暂时使用【授予组织】的编码和名字，假如不能重复，则再加上【所属组织】的编码和名字
                productAuth["fnumber"] = org["fnumber"];    // 
                productAuth["fname"] = org["fname"];        // 根据【授予组织】的名称+”-”+【城市】的名称
                productAuth["forgid"] = orgId;              // 根据《门店》对应的《组织》

                //http://dmp.jienor.com:81/zentao/bug-view-23566.html 商品授权清单中组织类型改成跟组织中组织类型字段类型同步，即在保存时需要处理字段逻辑
                productAuth["forgtype"] = org["forgtype"];
                //productAuth["fcityid"] = cityId;          // 根据《送达方》表头的【城市】

                // 取门店数据
                //新逻辑：fmusibrand=1 表示慕思品牌，是慕思品牌的才会添加到门店的商品授权清单中。
                var matchSeries = storeSerieses
                    .Where(s => Convert.ToString(s["fstoreid"]).EqualsIgnoreCase(storeExtenalId) && Convert.ToString(s["fmusibrand"]).EqualsIgnoreCase("1"));

                // 授权品牌/系列
                var entrys = (DynamicObjectCollection)productAuth["fproductauthbs"];
                if (store["fmainorgid"].ToString().EqualsIgnoreCase(userCtx.TopCompanyId)&&(store["fisonlinestore"].ToString().IsNullOrEmptyOrWhiteSpace()|| store["fisonlinestore"].ToString().EqualsIgnoreCase("1"))) {
                    entrys?.Clear();
                }
                foreach (var item in matchSeries.GroupBy(s => Convert.ToString(s["fbrandid"])))
                {
                    var fbrandid = item.Key;

                    var entry = entrys.FirstOrDefault(s => Convert.ToString(s["fbrandid"]).EqualsIgnoreCase(fbrandid));
                    if (entry == null)
                    {
                        entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                        entry["fbrandid"] = fbrandid;       // 根据《门店与系列》的【品牌】
                        entrys.Add(entry);
                    }

                    List<string> serieIds = Convert.ToString(entry["fserieid"]).SplitKey(",");
                    List<string> serieNames = Convert.ToString(entry["fserieid_txt"]).SplitKey(",");

                    foreach (var serie in item)
                    {
                        var parentId = Convert.ToString(serie["fparentserieid"]);
                        var parentName = Convert.ToString(serie["fparentseriename"]);
                        var serieId = Convert.ToString(serie["fserieid"]);
                        var serieName = Convert.ToString(serie["fseriename"]);

                        string fforbidstatus = Convert.ToString(serie["fforbidstatus"]);
                        bool forbidStatus = fforbidstatus.EqualsIgnoreCase("1") || fforbidstatus.EqualsIgnoreCase("true");

                        // 如果父级系列为空，使用当前系列
                        // 经销商授权, 送达方授权, 门店授权接口，系列匹配上级系列(业绩品牌)
                        // http://dmp.jienor.com:81/zentao/task-view-35738.html
                        if (!parentId.IsNullOrEmptyOrWhiteSpace())
                        {
                            serieId = parentId;
                            serieName = parentName;
                        }

                        // 如果禁用，且包括此系列，则删除
                        if (forbidStatus && serieIds.Contains(serieId))
                        {
                            var index = serieIds.IndexOf(serieId);
                            serieIds.RemoveAt(index);
                            serieNames.RemoveAt(index);
                            continue;
                        }

                        // 如果非禁用，且不包括此系列，则添加
                        if (!forbidStatus && !serieIds.Contains(serieId))
                        {
                            serieIds.Add(serieId);
                            serieNames.Add(serieName);
                            continue;
                        }
                    }

                    // 过滤系列id为空的情况
                    for (int i = serieIds.Count - 1; i >= 0; i--)
                    {
                        var id = serieIds[i];
                        if (id.IsNullOrEmptyOrWhiteSpace())
                        {
                            serieIds.RemoveAt(i);
                            serieNames.RemoveAt(i);
                        }
                    }

                    var fserieid = string.Join(",", serieIds);
                    var fserieid_txt = string.Join(",", serieNames);

                    entry["fserieid"] = fserieid;       // 根据《门店与系列》的【系列】
                    entry["fserieid_txt"] = fserieid_txt;
                }

                savingProductAuths.Add(productAuth);
            }

            if (savingProductAuths.Any())
            {
                var productAuthService = userCtx.Container.GetService<IProductAuthService>();
                productAuthService.AddDefault(userCtx, savingProductAuths);

                var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                var result = gateway.InvokeBillOperation(userCtx, "ydj_productauth", savingProductAuths, "save",
                    new Dictionary<string, object>());
                result.ThrowIfHasError(true, "商品授权清单保存失败！");

                //userCtx.Container.GetService<IProductAuthService>()
                //    .AddDefault(userCtx, savingProductAuths);

                //userCtx.Container.GetService<IPrepareSaveDataService>()
                //    .PrepareDataEntity(userCtx, productAuthMeta, savingProductAuths.ToArray(), OperateOption.Create());

                //var dm = userCtx.Container.GetService<IDataManager>();
                //dm.InitDbContext(userCtx, productAuthDt);
                //dm.Save(savingProductAuths);
            }
        }

        /// <summary>
        /// 过滤重复的《门店与系列》
        /// </summary>
        /// <param name="storeSerieses"></param>
        /// <returns></returns>
        public List<DynamicObject> DistinctStoreSerieses(List<DynamicObject> storeSerieses)
        {
            var distinctStoreSerieses = new List<DynamicObject>();
            var keys = new HashSet<string>();

            // 门店+系列作主键，按修改时间倒序，取第一项
            foreach (var item in storeSerieses.OrderByDescending(s => Convert.ToDateTime(s["fmodifydate"])))
            {
                string key = $"{item["fstoreid"]}_{item["fserieid"]}";

                if (keys.Add(key))
                {
                    distinctStoreSerieses.Add(item);
                }
            }

            return distinctStoreSerieses;
        }

        /// <summary>
        /// 更新实控人信息
        /// 注：需要自行保存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="stores"></param>
        public void UpdateBoss(UserContext userCtx, IEnumerable<DynamicObject> stores)
        {
            stores = stores?
                   .Where(s => s["fsrcstoreid"].IsNullOrEmptyOrWhiteSpace());
            if (stores.IsNullOrEmpty()) return;

            // 外部实控人ID
            var bossIds = stores.Select(s => Convert.ToString(s["actualownerid"]));

            var bosses = userCtx.LoadBizDataById("ms_boss", bossIds);

            foreach (var store in stores)
            {
                // 外部实控人ID
                string actualownerid = Convert.ToString(store["actualownerid"]);

                var boss = bosses.FirstOrDefault(
                    s => Convert.ToString(s["id"]).EqualsIgnoreCase(actualownerid));

                if (boss == null) continue;

                // 填充实控人的编码
                store["actualownernumber"] = boss["fnumber"];
            }
        }

        /// <summary>
        /// 获取获取
        /// </summary>
        /// <param name="agentCtx"></param>
        /// <param name="agentIds"></param>
        /// <returns></returns>
        public List<DynamicObject> GetStoreList(UserContext topCtx, List<string> agentIds)
        {
            string str = string.Join(",", agentIds.Select(id => $"'{id}'"));
            string sql = $"select fid,fnumber,forgid from t_bas_store where  forgid in ({str}) and fforbidstatus = 0";
            return topCtx.Container.GetService<IDBService>().ExecuteDynamicObject(topCtx, sql).ToList();
        }

        #region 更新门店是否为新渠道门店
        public int UpdateStoreNewChannel(UserContext userCtx, string operationNo, List<string> storeIds = null)
        {
            int result = 0;
            // 1. 获取所有新渠道系列信息
            string seriessql = "select fid, fnumber, fbrandid from t_ydj_series with(nolock) where fisnewchannel = '1' and fforbidstatus='0'";
            var newChannelSeriesData = userCtx.ExecuteDynamicObject(seriessql.ToString(), null).ToList();
            var newChannelBrands = new HashSet<string>();
            var newChannelSeries = new HashSet<string>();
            foreach (var s in newChannelSeriesData)
            {
                if (s["fid"] != null) newChannelSeries.Add(s["fid"].ToString());
                if (s["fbrandid"] != null) newChannelBrands.Add(s["fbrandid"].ToString());
            }

            // 2. 分批处理参数
            const int batchSize = 1000;
            var logService = userCtx.Container.GetService<ILogService>();
            var dbService = userCtx.Container.GetService<IDBServiceEx>();

            // 3. 获取所有需要处理的门店ID
            List<string> allStoreIds = storeIds != null&& storeIds?.Count>0 ? storeIds: GetAllStoreIds(userCtx);


            // 4. 进行分批处理
            int totalCount = allStoreIds.Count;
            var loopCount = Convert.ToInt32(Math.Ceiling(totalCount / 1.0 / batchSize));
            for (int i = 0; i < loopCount; i++)
            {
                var batchStoreIds = allStoreIds.Skip(batchSize * i).Take(batchSize);
                if (!batchStoreIds.Any()) continue;
                try
                {
                    var batchData = GetStoreBatchData(userCtx, batchStoreIds, userCtx.TopCompanyId);
                    // 6. 计算需要更新的门店状态
                    var (updates, changeLog) = CalculateStoreUpdates(
                        batchData,
                        newChannelBrands,
                        newChannelSeries,
                        operationNo
                    );

                    // 7. 批量更新门店状态
                    if (updates.Count > 0)
                    {
                        result += updates.Count;
                        BatchUpdateStoreStatus(dbService, userCtx, updates);
                    }

                    // 8. 批量记录日志
                    if (changeLog.Count > 0)
                    {
                        logService.BatchWriteLog(userCtx, changeLog);
                    }
                }
                catch (Exception e)
                {
                   
                }
            }
            return result;
        }

        /// <summary>
        ///  获取所有门店ID
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <returns>返回所有门店(总部创建)</returns>
        private List<string> GetAllStoreIds(UserContext userCtx)
        {
             string storeIdSql = $"SELECT fid FROM t_bas_store WHERE fforbidstatus = '0' and fmainorgid='{userCtx.TopCompanyId}'";
            return userCtx.ExecuteDynamicObject(storeIdSql, null)
                          .Select(d => d["fid"].ToString())
                          .ToList();
        }

        /// <summary>
        /// 获取门店授权信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="storeIds">门店主键集合</param>
        /// <param name="topCompanyId">总部id</param>
        /// <returns></returns>
        private Dictionary<string, (DynamicObject Item, List<(string BrandId, string SeriesIds)> AuthEntries)> GetStoreBatchData(UserContext userCtx, IEnumerable<string> storeIds, string topCompanyId)
        {
            // 安全处理ID列表，防止SQL注入
            var safeIds = storeIds.Select(id => id.Replace("'", "''")).ToList();
            string idList = string.Join("','", safeIds);

            string combinedSql = $@"
        SELECT s.fid, s.fisnewchannel, s.fnumber, 
               b.fbrandid, b.fserieid
        FROM t_bas_store s
        LEFT JOIN t_ydj_productauth a ON s.fid = a.forgid
        LEFT JOIN t_ydj_productauthbs b ON a.fid = b.fid
        WHERE a.fmainorgid = '{topCompanyId}'
          AND s.fid IN ('{idList}')
          AND s.fforbidstatus = '0' and  a.fforbidstatus = '0'";

            var combinedData = userCtx.ExecuteDynamicObject(combinedSql, null);
            var resultDict =new Dictionary<string, (DynamicObject Item, List<(string BrandId, string SeriesIds)> AuthEntries)>();

            var groupedData = combinedData.GroupBy(d => d["fid"].ToString());

            foreach (var row in combinedData)
            {
                string fid = row["fid"]?.ToString();
                if (string.IsNullOrEmpty(fid)) continue;

                if (!resultDict.TryGetValue(fid, out var storeData))
                {
                    storeData = (row, new List<(string, string)>());
                    resultDict[fid] = storeData;
                }

                string brandId = row["fbrandid"]?.ToString();
                string seriesIds = row["fserieid"]?.ToString();

                if (!string.IsNullOrEmpty(brandId))
                {
                    storeData.AuthEntries.Add((brandId, seriesIds));
                }
            }
            return resultDict;
        }

        /// <summary>
        /// 更新门店经营模式
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agents"></param>
        public void UpdateStoreType(UserContext userCtx, IEnumerable<DynamicObject> stores)
        {
            if (stores.IsNullOrEmpty()) return;

            //门店信息
            var agentids = stores.Select(s => Convert.ToString(s["fagentid"])).ToList();
            var sql = $@"
            select fid,fmanagemodel from t_bas_agent as s with (nolock) 
            where s.fid in ({agentids.JoinEx(",", true)}) ";

            var dbService = userCtx.Container.GetService<IDBService>();
            var objs = dbService.ExecuteDynamicObject(userCtx, sql);

            foreach (var store in stores)
            {
                string agentid = Convert.ToString(store["fagentid"]);

                var obj = objs.FirstOrDefault(
                    s => Convert.ToString(s["fid"]).EqualsIgnoreCase(agentid));

                if (obj == null) continue;
                //更新经销商关联销售组织（根据《客户销售组织与渠道关系》关联获取）
                store["ftype"] = obj["fmanagemodel"];
            }
        }

        /// <summary>
        /// 获取需修改新渠道状态的门店
        /// </summary>
        /// <param name="storeDict">门店数据</param>
        /// <param name="newChannelBrands">新渠道系列所属品牌</param>
        /// <param name="newChannelSeries">新渠道系列</param>
        /// <param name="operationNo">操作码</param>
        /// <returns></returns>
        private (Dictionary<string, bool> updates, List<LogEntry> changeLog) CalculateStoreUpdates(Dictionary<string, (DynamicObject Item, List<(string BrandId, string SeriesIds)> AuthEntries)> storeDict, HashSet<string> newChannelBrands, HashSet<string> newChannelSeries,string operationNo)
        {
            var updates = new Dictionary<string, bool>();
            var changeLog = new List<LogEntry>();
            string actionDesc = "";
            string opName = "";
            switch (operationNo)
            {
                case "updateProductAuth":
                    opName = "门店商品授权清单保存";
                    actionDesc = $"执行了【商品授权清单保存】操作";
                    break;
                case "updateStoreNewChannel":
                    opName = "更新新渠道门店";
                    actionDesc = $"执行了【更新新渠道门店】操作";
                    break;
                case "updateStoreSeries":
                    opName = "门店与系列更新";
                    actionDesc = $"【门店与系列更新】操作";
                    break;
            }

            foreach (var kv in storeDict)
            {
                var store = kv.Value.Item;
                bool currentStatus = store["fisnewchannel"].ToString().EqualsIgnoreCase("1");

                var hasNewChannel = false;

                // 检查全品牌授权
                var fullBrandAuth = kv.Value.AuthEntries
                    .Any(e =>string.IsNullOrEmpty(e.SeriesIds) &&
                             newChannelBrands.Contains(e.BrandId));

                // 检查系列授权
                var seriesAuth = kv.Value.AuthEntries
                    .Where(e => !string.IsNullOrEmpty(e.SeriesIds))
                    .SelectMany(e => e.SeriesIds.Split(','))
                    .Any(seriesId => newChannelSeries.Contains(seriesId.Trim()));
                hasNewChannel = fullBrandAuth || seriesAuth;
                if (currentStatus != hasNewChannel)
                {
                    string storeId = store["fid"].ToString();
                    updates[storeId] = hasNewChannel;

                    changeLog.Add(new LogEntry
                    {
                        BillIds = storeId,
                        BillNos = store["fnumber"].ToString(),
                        BillFormId = "bas_store",
                        OpName = opName,
                        OpCode = operationNo,
                        Content = $"{actionDesc},旧【新创新渠道标记】值：{(currentStatus ? "是" : "否")}," +
                                  $"新【新创新渠道标记】值：{(hasNewChannel ? "是" : "否")}",
                        DebugData = $"门店状态更新: {storeId}",
                        Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                        Level = Enu_LogLevel.Info.ToString(),
                        LogType = Enu_LogType.RecordType_03,
                    });
                }
            }

            return (updates, changeLog);
        }
        /// <summary>
        /// 批量更新门店新渠道值
        /// </summary>
        /// <param name="dbService">数据库服务</param>
        /// <param name="userCtx">上下文</param>
        /// <param name="updates">需修改的数据</param>
        private void BatchUpdateStoreStatus(IDBServiceEx dbService,UserContext userCtx, Dictionary<string, bool> updates)
        {
            if (updates.Count == 0) return;
            var caseStatements = new StringBuilder();
            foreach (var update in updates)
            {
                caseStatements.AppendLine($"WHEN '{update.Key.Replace("'", "''")}' THEN {(update.Value ? 1 : 0)}");
            }
            string updateSql = $@"
        UPDATE t_bas_store
        SET fisnewchannel = CASE fid
            {caseStatements}
        END,
        fmodifydate = GETDATE()
        WHERE fid IN ({string.Join(",", updates.Keys.Select(id => $"'{id.Replace("'", "''")}'"))})";
            dbService.Execute(userCtx, updateSql.ToString());
        }
        #endregion
    }

}
