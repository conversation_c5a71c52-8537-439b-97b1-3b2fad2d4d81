using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Service
{
    /// <summary>
    /// 库存水位线计算服务
    /// </summary>
    [InjectService]
    public class StockWaterLevelService
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]

        protected IDBService DBService { get; set; }

        private DynamicObject CurrentwaterlevelConfig { get; set; }

        public IOperationResult StockWaterlevelCalculate(UserContext userCtx, OperateOption option)
        {
            var result = userCtx.Container.GetService<IOperationResult>(); 
            var dbService = userCtx.Container.GetService<IDBService>();
            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            var priceTableName = string.Empty;
            DynamicObjectCollection data = null;
            DataTable dt = new DataTable();
           var tmpTableName = this.DBService.CreateTemporaryTableName(userCtx);

            var inventorywaterlevelConfig = userCtx.LoadBizDataByFilter("si_inventorywaterlevel", " fforbidstatus = 0 ");
            //优先获取经销商级别的
            this.CurrentwaterlevelConfig = inventorywaterlevelConfig.Where(o => Convert.ToString(o["fcontrollevel"]).EqualsIgnoreCase("2")
            && Convert.ToBoolean(o["fcalculateenable"])
            && Convert.ToString(o["fagentid"]).EqualsIgnoreCase(userCtx.Company))?.FirstOrDefault();
            if (this.CurrentwaterlevelConfig == null) 
            {
                //取不到再取全量的
                this.CurrentwaterlevelConfig = inventorywaterlevelConfig.Where(o => Convert.ToString(o["fcontrollevel"]).EqualsIgnoreCase("3")
                && Convert.ToBoolean(o["fcalculateenable"]))?.FirstOrDefault();
            }
            if (this.CurrentwaterlevelConfig == null)
            {
                result.SimpleMessage = "无有效配置表，请检查！";
                return result;
            }

            try
            {
                //查询经销商库存数据
                var querySql = $@"/*dialect*/select pd.fseltypeid,t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.fmainorgid,fstorehouseid,fstorelocationid,t2.fname as fstorehousename,t2.fwarehousetype,t1.fqty fqty,isnull(pdmx1.orgcount,0) orgcount
                                            into {tmpTableName}
                                            from t_stk_inventorylist as t1 with (nolock)
                                            left join t_bd_material as pd with (nolock) on pd.fid = t1.fmaterialid
                                            left join t_ydj_storehouse as t2 on t1.fstorehouseid = t2.fid
                                            left join (select pdmx.fid ,count(0) as orgcount from t_bd_materialsaleorg as pdmx with (nolock) where fdisablestatus='1' group by pdmx.fid ) as pdmx1 on pdmx1.fid = pd.fid
                                            where t1.fqty >0 and t1.fmainorgid ='{userCtx.Company}'  and pd.fmainorgid = '{userCtx.TopCompanyId}' and pd.fnumber != 'VFZ1-M001' ";
                dbService.ExecuteDynamicObject(userCtx, querySql);

                data = dbService.ExecuteDynamicObject(userCtx, $"select t1.fseltypeid,t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.fmainorgid,fstorehouseid,fstorelocationid,t1.fstorehousename,t1.fwarehousetype,t1.fqty,orgcount from {tmpTableName} as t1 ");

                var addData = data.Select(x => new
                {
                    fseltypeid = Convert.ToString(x["fseltypeid"]),
                    fmaterialid = Convert.ToString(x["fmaterialid"]),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    fstorehouseid = Convert.ToString(x["fstorehouseid"]).Trim(),
                    fstorelocationid = Convert.ToString(x["fstorelocationid"]).Trim(),
                    fstorehousename = Convert.ToString(x["fstorehousename"]).Trim(),
                    fwarehousetype = Convert.ToString(x["fwarehousetype"]).Trim(),
                    orgcount = Convert.ToDecimal(x?["orgcount"]?? 0M),
                    fstockintransitqty = 0M,
                    fqty = Convert.ToDecimal(x?["fqty"]??0M) ,
                    findbqty = 0M,
                    fsampleindbqty = 0M
                }).ToList();

                #region【总库存数】=A => 所有仓库汇总
                //【总库存数】=A => 所有仓库汇总
                var inventoryDatas_A = addData
                     .GroupBy(x => new { x.fmaterialid, x.fattrinfo_e, x.fcustomdesc })
                     .Select(o => new { fmaterialid = o.Key.fmaterialid, fattrinfo_e = o.Key.fattrinfo_e, fcustomdesc = o.Key.fcustomdesc, ftotalstockqty = o.Sum(x => x.fqty) })
                     .ToList();
                #endregion

                #region【样品】=B => 仓库类型=“门店仓”的所有仓库汇总
                //【样品】=B => 仓库类型=“门店仓”的所有仓库汇总
                var inventoryDatas_B = addData
                    //.Where(o => o.fstorehousename == "门店仓")
                    .Where(o=> o.fwarehousetype == "warehouse_02")
                    .GroupBy(x => new { x.fmaterialid, x.fattrinfo_e, x.fcustomdesc })
                    .Select(o => new { fmaterialid = o.Key.fmaterialid, fattrinfo_e = o.Key.fattrinfo_e, fcustomdesc = o.Key.fcustomdesc, fsampleqty = o.Sum(x => x.fqty) })
                    .ToList();
                #endregion

                #region 【订单未清数】=E
                //（【单据头.需转单】= 否 并且 【单据头.作废状态】= 否 并且 【商品明细.行关闭状态】=“正常”/“部分关闭”） 或者
                //（【单据头.需转单】= 是 并且 【单据头.作废状态】= 否 并且 【商品明细.行关闭状态】=“正常”/“部分关闭” 并且【出现货】= 是 ）
                var strSql = $@"/*dialect*/select t1.fproductid as fmaterialid,fattrinfo_e,t1.fcustomdes_e as fcustomdesc,t0.fmainorgid,sum(funstockoutqty) as funstockoutqty 
                                            from t_ydj_order as t0 with (nolock)
                                            inner join t_ydj_orderentry as t1 with (nolock) on t0.fid = t1.fid 
                                            inner join t_bd_billtype as t2 with (nolock) on t2.fid = t0.fbilltype
                                            where  t0.fmainorgid='{userCtx.Company}' 
                                            and t2.fname not in ('门店上样','门店下样','v6定制柜合同','V6全屋定制合同','上样销售合同') 
                                            and ((fneedtransferorder = 0 and fcancelstatus = 0 and t1.fclosestatus in ('0','2')  )
                                            or (fneedtransferorder = 1 and fcancelstatus = 0 and t1.fclosestatus in ('0','2') and fisoutspot='1'  )) 
                                            group by t1.fproductid ,fattrinfo_e,t1.fcustomdes_e ,t0.fmainorgid";

                var inventoryDataE = dbService.ExecuteDynamicObject(userCtx, strSql);
                var inventoryDatas_E = inventoryDataE.Select(x => new
                {
                    fmaterialid = Convert.ToString(x["fmaterialid"]),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    fmainorgid = Convert.ToString(x["fmainorgid"]).Trim(),
                    funstockoutqty = Convert.ToDecimal(x["funstockoutqty"] ?? 0M)
                }).ToList();
                #endregion

                #region 【滞销库存】=C
                //【商品】【辅助属性】【定制说明】维度匹配《库龄分析报表》，得到{【仓库.仓库类型】不等于“门店仓” 并且 【在库天数】> 365} 的总数量。
                 strSql = $@"/*dialect*/ 
                            select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.fmainorgid,t1.fstorehouseid, fstorehousename,t1.fwarehousetype,t3.fqty
                            from {tmpTableName} as t1 with (nolock)
                            left join (select tt.fmainorgid,tt.fstorehouseid,tt.fmaterialid,tt.fattrinfo_e,tt.fcustomdesc,sum(fbizqty) as fqty from 
                            t_ydj_stockageanalysis as tt with (nolock) where tt.fmainorgid ='{userCtx.Company}' and tt.finstockday > 365 
                            group by tt.fmaterialid,tt.fattrinfo_e,tt.fcustomdesc,tt.fmainorgid,tt.fstorehouseid
                            )  as t3 on t3.fmainorgid = t1.fmainorgid and t3.fmaterialid = t1.fmaterialid and t3.fattrinfo_e = t1.fattrinfo_e and t3.fcustomdesc = t1.fcustomdesc and t3.fstorehouseid = t1.fstorehouseid 
                            where t1.fmainorgid ='{userCtx.Company}' 
                            group by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.fmainorgid,t1.fstorehouseid ,t1.fstorehousename,t1.fwarehousetype,t3.fqty ";
                var inventoryDataC = dbService.ExecuteDynamicObject(userCtx, strSql);

                var inventoryDatas = inventoryDataC
                    .Select(x => new
                {
                    fmaterialid = Convert.ToString(x["fmaterialid"]),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    fmainorgid = Convert.ToString(x["fmainorgid"]).Trim() ,
                    fstorehousename = Convert.ToString(x["fstorehousename"]),
                    fwarehousetype = Convert.ToString(x["fwarehousetype"]),
                    fqty = Convert.ToDecimal(x["fqty"] ?? 0M) 
                }).Where(o=>  o.fwarehousetype != "warehouse_02").ToList();

                var inventoryDatas_C = inventoryDatas.GroupBy(o => new { o.fmaterialid, o.fattrinfo_e, o.fcustomdesc })
                    .Select(o => new { fmaterialid = o.Key.fmaterialid, fattrinfo_e = o.Key.fattrinfo_e, fcustomdesc = o.Key.fcustomdesc, funsoldqty = o.Sum(x => x.fqty) })
                    .ToList();
                #endregion

                #region 【采购在途】=FG
                //依据【商品】【辅助属性】【定制说明】维度匹配《采购订单》，得到{【单据头.总部合同状态】=“已终审” 并且 【商品明细.行关闭状态】=正常/部分关闭}的采购订单的商品行{【采购数量】-【采购入库数量】+【采购退换数量】}的汇总。
                strSql = $@"/*dialect*/select t1.fmaterialid,fattrinfo_e,t1.fcustomdes_e as fcustomdesc,t0.fmainorgid,sum(fqty-fbizinstockqty+fbizreturnqty) as fstockintransitqty 
                                        from t_ydj_purchaseorder as t0 with (nolock)
                                        inner join t_ydj_poorderentry as t1 with (nolock) on t0.fid = t1.fid  
                                        where  t0.fmainorgid='{userCtx.Company}'  and t0.fcancelstatus = '0' and t0.fhqderstatus ='03' and t1.fclosestatus_e in ('0','2') 
                                        group by t1.fmaterialid ,fattrinfo_e,t1.fcustomdes_e ,t0.fmainorgid";

                var inventoryDataFG = dbService.ExecuteDynamicObject(userCtx, strSql);
                var inventoryDatas_FG = inventoryDataFG.Select(x => new
                {
                    fmaterialid = Convert.ToString(x["fmaterialid"]),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    fmainorgid = Convert.ToString(x["fmainorgid"]).Trim(),
                    fstockintransitqty = Convert.ToDecimal(x["fstockintransitqty"] ?? 0M)
                }).ToList();

                #endregion

                #region 【总销售数量】【统计月份数】【月均销售数量】
                //依据【商品 + 辅助属性 + 定制说明】匹配《月均销售数量表》，得到库存维度对应的【总销售数量】【统计月份数】【月均销售数量】

                strSql = $@"/*dialect*/
                            select t0.fmaterialid,t0.fattrinfo_e,t0.fcustomdesc,t1.fmainorgid,t1.fsumqty,t1.fmonthqty,favgsaleqty from {tmpTableName} as t0 
                            left join t_ydj_productmonthlysales as t1
                            on t0.fmainorgid = t1.fmainorgid and t0.fmaterialid =t1.fproductid and t0.fattrinfo_e = t1.fattrinfo_e and t0.fcustomdesc = t1.fcustomdes
                            where t0.fmainorgid = '{userCtx.Company}'
                            union 
                            select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e as fcustomdesc,t0.fmainorgid,t2.fsumqty,t2.fmonthqty,t2.favgsaleqty
                            from t_ydj_purchaseorder as t0 with (nolock)
                            inner join t_ydj_poorderentry as t1 with (nolock) on t0.fid = t1.fid  
                            left join t_ydj_productmonthlysales as t2
                            on t0.fmainorgid = t2.fmainorgid and t1.fmaterialid =t2.fproductid and t1.fattrinfo_e = t2.fattrinfo_e and t1.fcustomdes_e = t2.fcustomdes
                            where  t0.fmainorgid='{userCtx.Company}' and t0.fhqderstatus ='03' and t1.fclosestatus_e in ('0','2') 
                            union
                            select fmaterialid,fattrinfoto_e as fattrinfo_e,fcustomdesc ,'{userCtx.Company}' as fmainorgid,t2.fsumqty,t2.fmonthqty,t2.favgsaleqty
                            from t_stk_invtransferentry as transmx with (nolock) 
                             left join t_ydj_productmonthlysales as t2  on t2.fmainorgid = '{userCtx.Company}' and transmx.fmaterialid =t2.fproductid and transmx.fattrinfo_e = t2.fattrinfo_e and transmx.fcustomdesc = t2.fcustomdes
                            where exists(select 1 from t_stk_invtransfer as trans with (nolock) where trans.fmainorgid ='{userCtx.Company}'
                            and trans.fid = transmx.fid 
                            and fisstockout = 1 and trans.fstatus !='E' ) 
                            ";
                var inventoryDataMonths = dbService.ExecuteDynamicObject(userCtx, strSql);

                var inventoryDataMonth = inventoryDataMonths
                    .Select(x => new
                    {
                        fmaterialid = Convert.ToString(x["fmaterialid"]),
                        fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                        fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                        fmainorgid = Convert.ToString(x["fmainorgid"]) ,
                        fsumqty = Convert.ToDecimal(x["fsumqty"] ?? 0M),
                        fmonthqty = Convert.ToDecimal(x["fmonthqty"] ?? 0M),
                        favgsaleqty = Convert.ToDecimal(x["favgsaleqty"] ?? 0M) 
                    }).ToList();

                #endregion

                #region 【采购在途-无库存】=FGUN
                //依据【商品】【辅助属性】【定制说明】维度匹配《采购订单》，得到{【单据头.总部合同状态】=“已终审” 并且 【商品明细.行关闭状态】=正常/部分关闭}的采购订单的商品行{【采购数量】-【采购入库数量】+【采购退换数量】}的汇总。
                strSql = $@"/*dialect*/select t1.fmaterialid, t1.fattrinfo_e,t1.fcustomdes_e as fcustomdesc,t0.fmainorgid,sum(fqty-fbizinstockqty+fbizreturnqty) as fstockintransitqty , isnull(pdmx1.orgcount,0) orgcount
                                        from t_ydj_purchaseorder as t0 with (nolock)
                                        inner join t_ydj_poorderentry as t1 with (nolock) on t0.fid = t1.fid   
                                        inner join t_bd_material as pd with (nolock) on pd.fid = t1.fmaterialid
                                        left join (select pdmx.fid ,count(0) as orgcount from t_bd_materialsaleorg as pdmx with (nolock) where fdisablestatus='1' group by pdmx.fid ) as pdmx1 on pdmx1.fid = t1.fmaterialid
                                        where  t0.fmainorgid='{userCtx.Company}' and t0.fcancelstatus = '0'  and pd.fsuiteflag != '1' and t0.fhqderstatus ='03' and t1.fclosestatus_e in ('0','2') 
                                        and not exists (select 1 from t_stk_inventorylist as inv with (nolock) where inv.fmainorgid = t0.fmainorgid and inv.fmaterialid = t1.fmaterialid and inv.fattrinfo_e = t1.fattrinfo_e and inv.fcustomdesc = t1.fcustomdes_e and inv.fqty >0)
                                        group by t1.fmaterialid , t1.fattrinfo_e,t1.fcustomdes_e ,t0.fmainorgid ,pdmx1.orgcount";

                var inventoryDataFGUN = dbService.ExecuteDynamicObject(userCtx, strSql);
                var inventoryDatas_FGUN = inventoryDataFGUN.Select(x => new
                { 
                    fseltypeid = "",
                    fmaterialid = Convert.ToString(x["fmaterialid"]),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    fstorehouseid = "",
                    fstorelocationid = "",
                    fstorehousename = "",
                    fwarehousetype = "",
                    orgcount = Convert.ToDecimal(x["orgcount"] ?? 0M),
                    fstockintransitqty = Convert.ToDecimal(x["fstockintransitqty"] ?? 0M),
                    fqty = 0M ,
                    findbqty = 0M,
                    fsampleindbqty = 0M
                }).ToList();
                //追加采购无库存的数据
                addData.AddRange(inventoryDatas_FGUN);

                #endregion 

                #region 计算分布式调拨在途数量-有库存记录              
                strSql = $@"/*dialect*/		
                            select fmaterialid,fattrinfoto_e as fattrinfo_e,fcustomdesc,fqty,t2.fwarehousetype
                            from t_stk_invtransferentry as transmx with (nolock)
                            left join t_ydj_storehouse as t2 on transmx.fstorehouseidto = t2.fid
                            where exists(select 1 from t_stk_invtransfer as trans with (nolock) where trans.fmainorgid ='{userCtx.Company}'
                            and trans.fid = transmx.fid 
                            and fisstockout = 1 and trans.fstatus !='E' )";

                var inventoryDatasInDB = dbService.ExecuteDynamicObject(userCtx, strSql);
                var inventoryDataInDB = inventoryDatasInDB
                    .Select(x => new
                {
                    fmaterialid = Convert.ToString(x["fmaterialid"]),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    fmainorgid = userCtx.Company,
                    fwarehousetype = Convert.ToString(x["fwarehousetype"]).Trim(),
                    fqty = Convert.ToDecimal(x["fqty"] ?? 0M)
                }).ToList();
                //样品调拨在途-门店仓
                var inventoryDataInDB_YP = inventoryDataInDB.Where(o=>o.fwarehousetype == "warehouse_02")
                    .GroupBy(x => new { x.fmaterialid, x.fattrinfo_e, x.fcustomdesc })
                    .Select(o => new { fmaterialid = o.Key.fmaterialid, fattrinfo_e = o.Key.fattrinfo_e, fcustomdesc = o.Key.fcustomdesc, fqty = o.Sum(x => x.fqty) })
                     .ToList();
                //总仓调拨在途-非门店仓
                var inventoryDataInDB_ZC = inventoryDataInDB.Where(o => o.fwarehousetype != "warehouse_02")
                    .GroupBy(x => new { x.fmaterialid, x.fattrinfo_e, x.fcustomdesc })
                    .Select(o => new { fmaterialid = o.Key.fmaterialid, fattrinfo_e = o.Key.fattrinfo_e, fcustomdesc = o.Key.fcustomdesc, fqty = o.Sum(x => x.fqty) })
                    .ToList();

                #endregion

                #region 计算分布式调拨在途数量-无库存记录 
                strSql = $@"/*dialect*/		
                            select fmaterialid,fattrinfoto_e as fattrinfo_e,fcustomdesc,fqty as fsampleindbqty,0 as findbqty,t2.fwarehousetype, isnull(pdmx1.orgcount,0) orgcount
                            from t_stk_invtransferentry as transmx with (nolock)
                            left join t_ydj_storehouse as t2 on transmx.fstorehouseidto = t2.fid
                            left join (select pdmx.fid ,count(0) as orgcount from t_bd_materialsaleorg as pdmx with (nolock) where fdisablestatus='1' group by pdmx.fid ) as pdmx1 on pdmx1.fid = transmx.fmaterialid
                            where exists(select 1 from t_stk_invtransfer as trans with (nolock) where trans.fmainorgid ='{userCtx.Company}'
                            and trans.fid = transmx.fid 
                            and fisstockout = 1 and trans.fstatus !='E' )
                            and not exists (select 1 from t_stk_inventorylist as inv with (nolock) where inv.fmainorgid ='{userCtx.Company}' and inv.fmaterialid = transmx.fmaterialid and inv.fattrinfo_e = transmx.fattrinfo_e and inv.fcustomdesc = transmx.fcustomdesc and inv.fqty >0)
                            and fwarehousetype = 'warehouse_02'
                            union
                            select fmaterialid,fattrinfoto_e as fattrinfo_e,fcustomdesc,0 as fsampleindbqty,fqty as findbqty,t2.fwarehousetype, isnull(pdmx1.orgcount,0) orgcount
                            from t_stk_invtransferentry as transmx with (nolock)
                            left join t_ydj_storehouse as t2 on transmx.fstorehouseidto = t2.fid
                            left join (select pdmx.fid ,count(0) as orgcount from t_bd_materialsaleorg as pdmx with (nolock) where fdisablestatus='1' group by pdmx.fid ) as pdmx1 on pdmx1.fid = transmx.fmaterialid
                            where exists(select 1 from t_stk_invtransfer as trans with (nolock) where trans.fmainorgid ='{userCtx.Company}'
                            and trans.fid = transmx.fid 
                            and fisstockout = 1 and trans.fstatus !='E' )
                            and not exists (select 1 from t_stk_inventorylist as inv with (nolock) where inv.fmainorgid ='{userCtx.Company}' and inv.fmaterialid = transmx.fmaterialid and inv.fattrinfo_e = transmx.fattrinfo_e and inv.fcustomdesc = transmx.fcustomdesc and inv.fqty >0)
                            and fwarehousetype != 'warehouse_02'";

                var inventoryDataDBUN = dbService.ExecuteDynamicObject(userCtx, strSql);
                var inventoryDatas_DBUN = inventoryDataDBUN.Select(x => new
                {
                    fseltypeid = "",
                    fmaterialid = Convert.ToString(x["fmaterialid"]),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    fstorehouseid = "",
                    fstorelocationid = "",
                    fstorehousename = "",
                    fwarehousetype = "",
                    orgcount = Convert.ToDecimal(x["orgcount"] ?? 0M),
                    fstockintransitqty = 0M,
                    fqty = 0M,
                    findbqty = Convert.ToDecimal(x["findbqty"] ?? 0M),
                    fsampleindbqty = Convert.ToDecimal(x["fsampleindbqty"] ?? 0M)
                }).ToList();

                //追加调拨无库存的数据
                addData.AddRange(inventoryDatas_DBUN);
                #endregion

                var saveDatas = (from t in addData
                                 join a in inventoryDatas_A on new { fmaterialid = t.fmaterialid, fattrinfo_e = t.fattrinfo_e, fcustomdesc = t.fcustomdesc }
                                 equals new { fmaterialid = a.fmaterialid, fattrinfo_e = a.fattrinfo_e, fcustomdesc = a.fcustomdesc } into tempA
                                 from an in tempA.DefaultIfEmpty()
                                 join b in inventoryDatas_B on new { fmaterialid = t.fmaterialid, fattrinfo_e = t.fattrinfo_e, fcustomdesc = t.fcustomdesc }
                                 equals new { fmaterialid = b.fmaterialid, fattrinfo_e = b.fattrinfo_e, fcustomdesc = b.fcustomdesc } into tempB
                                 from bn in tempB.DefaultIfEmpty()
                                 join c in inventoryDatas_C on new { fmaterialid = t.fmaterialid, fattrinfo_e = t.fattrinfo_e, fcustomdesc = t.fcustomdesc }
                                 equals new { fmaterialid = c.fmaterialid, fattrinfo_e = c.fattrinfo_e, fcustomdesc = c.fcustomdesc } into tempC
                                 from cn in tempC.DefaultIfEmpty()
                                 join e in inventoryDatas_E on new { fmaterialid = t.fmaterialid, fattrinfo_e = t.fattrinfo_e, fcustomdesc = t.fcustomdesc }
                                 equals new { fmaterialid = e.fmaterialid, fattrinfo_e = e.fattrinfo_e, fcustomdesc = e.fcustomdesc } into tempE
                                 from en in tempE.DefaultIfEmpty()
                                 join fg in inventoryDatas_FG on new { fmaterialid = t.fmaterialid, fattrinfo_e = t.fattrinfo_e, fcustomdesc = t.fcustomdesc }
                                 equals new { fmaterialid = fg.fmaterialid, fattrinfo_e = fg.fattrinfo_e, fcustomdesc = fg.fcustomdesc } into tempFG
                                 from fgn in tempFG.DefaultIfEmpty()
                                 join month in inventoryDataMonth on new { fmaterialid = t.fmaterialid, fattrinfo_e = t.fattrinfo_e, fcustomdesc = t.fcustomdesc }
                                 equals new { fmaterialid = month.fmaterialid, fattrinfo_e = month.fattrinfo_e, fcustomdesc = month.fcustomdesc } into tempMonth
                                 from monthn in tempMonth.DefaultIfEmpty()
                                 join dbyp in inventoryDataInDB_YP on new { fmaterialid = t.fmaterialid, fattrinfo_e = t.fattrinfo_e, fcustomdesc = t.fcustomdesc }
                                 equals new { fmaterialid = dbyp.fmaterialid, fattrinfo_e = dbyp.fattrinfo_e, fcustomdesc = dbyp.fcustomdesc } into tempDB_YP
                                 from dbypn in tempDB_YP.DefaultIfEmpty()
                                 join dbzc in inventoryDataInDB_ZC on new { fmaterialid = t.fmaterialid, fattrinfo_e = t.fattrinfo_e, fcustomdesc = t.fcustomdesc }
                                 equals new { fmaterialid = dbzc.fmaterialid, fattrinfo_e = dbzc.fattrinfo_e, fcustomdesc = dbzc.fcustomdesc } into tempDB_ZC
                                 from dbzcn in tempDB_ZC.DefaultIfEmpty()
                                     //from fg in unionDatas

                                 select new
                                 {
                                     fseltypeid = t.fseltypeid,
                                     fmaterialid = t.fmaterialid,
                                     fattrinfo_e = t.fattrinfo_e,
                                     fcustomdesc = t.fcustomdesc,
                                     orgcount = t.orgcount,
                                     ftotalstockqty = an == null ? 0.00M : an.ftotalstockqty,
                                     fsampleqty = bn == null ? 0.00M : bn.fsampleqty,
                                     funsoldqty = cn == null ? 0.00M : cn.funsoldqty,
                                     fexistorderqty = en == null ? 0.00M : en.funstockoutqty,
                                     fstockintransitqty = fgn == null ? 0.00M : fgn.fstockintransitqty,
                                     //计算【备货数】=【总库存】-【样品】-【订单未清数】-【滞销库存】+【采购在途】= A - B - C - E + FG
                                     fstockupqty =  0M,
                                     //月销售数量关联字段【总销售数量】【统计月份数】【月均销售数量】
                                     fmonth = monthn?.fmonthqty ?? 0M,
                                     favgsaleqty = monthn?.favgsaleqty ?? 0M,
                                     ftotalqty = monthn?.fsumqty ?? 0M,
                                     //【库销比】=【备货数】/【月均销售数量】
                                     fstocktosalerate =0M,
                                     findbqty = dbzcn == null ? 0.00M : dbzcn.fqty ,
                                     fsampleindbqty = dbypn == null ? 0.00M : dbypn.fqty 
                                 }).Distinct().ToList();

                dt.Columns.Add("fmaterialid");
                //dt.Columns.Add("fattrinfo");
                dt.Columns.Add("fattrinfo_e");
                dt.Columns.Add("fcustomdesc"); 
                dt.Columns.Add("ftotalstockqty", typeof(decimal));
                dt.Columns.Add("fsampleqty", typeof(decimal));
                dt.Columns.Add("funsoldqty", typeof(decimal)); 
                dt.Columns.Add("fexistorderqty", typeof(decimal));
                dt.Columns.Add("fstockintransitqty", typeof(decimal));
                dt.Columns.Add("fstockupqty", typeof(decimal));
                dt.Columns.Add("fmonth", typeof(decimal));
                dt.Columns.Add("favgsaleqty", typeof(decimal));
                dt.Columns.Add("ftotalqty", typeof(decimal));
                dt.Columns.Add("fstocktosalerate", typeof(decimal));
                //dt.Columns.Add("redline", typeof(decimal));
                //dt.Columns.Add("greenline", typeof(decimal));
                //库存水位线状态
                dt.Columns.Add("fstockwaterline", typeof(string));
                dt.Columns.Add("fstopproduction", typeof(int));
                dt.Columns.Add("fpurwaitqty", typeof(decimal));
                dt.Columns.Add("fstockwaitqty", typeof(decimal));
                dt.Columns.Add("findbqty", typeof(decimal));
                dt.Columns.Add("fsampleindbqty", typeof(decimal));
                dt.Columns.Add("pkid");

                dt.BeginLoadData();
                Random rd = new Random();
                var CurrentwaterlevelConfigEntrys = this.CurrentwaterlevelConfig["fexceptionmodelsentity"] as DynamicObjectCollection;
                Parallel.ForEach(saveDatas, saveData =>
                {
                    //计算【备货数】=【总库存】-【样品】-【订单未清数】-【滞销库存】+【采购在途】+ 【总仓调拨在途】= A - B - C - E + FG +H
                    decimal fstockupqty = saveData.ftotalstockqty - saveData.fsampleqty - saveData.fexistorderqty - saveData.funsoldqty + saveData.fstockintransitqty +saveData.findbqty;
                    //计算库销比
                    var fstocktosalerate = 0M;
                    if (saveData.favgsaleqty != 0M && fstockupqty <= 0M)
                    {
                        fstocktosalerate = 0M;
                    }
                    else if (saveData.favgsaleqty == 0M)
                    {
                        fstocktosalerate = fstockupqty;
                    }
                    else 
                    {
                        fstocktosalerate = fstockupqty / saveData.favgsaleqty;
                    }
                    //2、新增字段【待采数】：fpurwaitqty 6.29
                    //①当【备货数】< 0：取该负数对应的“正数”，作为待采数。
                    //②当【备货数】>= 0：待采数为0。
                    var fpurwaitqty = 0M;
                    //if (fstockupqty < 0) 
                    //{
                    //    fpurwaitqty = Math.Abs(fstockupqty);
                    //}
                    //【待采数】的逻辑，6.30客户反馈不用剔除滞销品，需要再调整一下：
                    //①若【总库存】-【样品】+【采购在途】>=【订单未清数】：则，待采数 = 0。
                    //②若【总库存】-【样品】+【采购在途】<【订单未清数】：则，待采数 =【订单未清数】-（【总库存】-【样品】+【采购在途】）。
                    if (saveData.ftotalstockqty - saveData.fsampleqty + saveData.fstockintransitqty < saveData.fexistorderqty) 
                    {
                        fpurwaitqty = saveData.fexistorderqty - (saveData.ftotalstockqty - saveData.fsampleqty + saveData.fstockintransitqty);
                    }
                    //3、新增字段【库存待发数】：fstockwaitqty
                    //①当【订单未清数】< (【总库存】-【样品】）：库存待发数 =【订单未清数】
                    //②当【订单未清数】>= (【总库存】-【样品】）：库存待发数 =【总库存】-【样品】
                    var fstockwaitqty = 0M;
                    if (saveData.fexistorderqty < (saveData.ftotalstockqty - saveData.fsampleqty))
                    {
                        fstockwaitqty = saveData.fexistorderqty;
                    }
                    else 
                    {
                        fstockwaitqty = (saveData.ftotalstockqty - saveData.fsampleqty);
                    }
                    decimal redline = Convert.ToDecimal(this.CurrentwaterlevelConfig?["fredline"] ?? 0M);
                    decimal greenline = Convert.ToDecimal(this.CurrentwaterlevelConfig?["fgreenline"] ?? 0M);
                    //计算水位线状态
                    var configEntry = CurrentwaterlevelConfigEntrys.Where(o => Convert.ToBoolean(o["fcalculateenable_spu"]) &&
                                                                                Convert.ToString(o["fseltypeid"]).EqualsIgnoreCase(saveData.fseltypeid))?.FirstOrDefault();
                    if (configEntry != null)
                    {
                        redline = Convert.ToDecimal(configEntry?["fredline_spu"] ?? 0M);
                        greenline = Convert.ToDecimal(configEntry?["fgrennline_spu"] ?? 0M);
                    }
                    //'0':'','1':'红灯','2':'绿灯','3':'黄灯'
                    //二、计算【库存水位线状态】
                    //1、当【是否停产商品】= 是，则必定更新状态为“红灯”。
                    //2、当【是否停产商品】= 否：
                    //①.当【库销比】>【红灯线】：则更新状态为“红灯”。
                    //②.当【库销比】<【绿灯线】：则更新状态为“绿灯”。
                    //③.当【绿灯线】=<【库销比】<=【红灯线】：则更新状态为“黄灯”。
                    var fstockwaterline = "0";
                    //商品停产,即无启用销售组织
                    if (saveData.orgcount > 0)
                    {
                        if (fstocktosalerate > redline)
                        {
                            fstockwaterline = "1";
                        }
                        else if (fstocktosalerate < greenline)
                        {
                            fstockwaterline = "2";
                        }
                        else 
                        {
                            fstockwaterline = "3";
                        }
                    }
                    else
                    {
                        fstockwaterline = "1";
                    }
                    //备货数如果为负数则为0
                    if (fstockupqty <= 0M) 
                    {
                        fstockupqty = 0M;
                        //备货数如果为负数,库销比也为0
                        fstocktosalerate = 0M;
                    }
                    //是否停产
                    int fstopproduction = saveData.orgcount == 0 ? 1 :0;
                    var pkid = DateTime.Now.ToString("yyyyMMddHHmmssfff") + Guid.NewGuid().ToString("N").Substring(0, 10);
                    lock (dt.Rows)
                    {
                        dt.Rows.Add(saveData.fmaterialid, saveData.fattrinfo_e, saveData.fcustomdesc,saveData.ftotalstockqty, saveData.fsampleqty,saveData.funsoldqty,
                            saveData.fexistorderqty,saveData.fstockintransitqty, fstockupqty, saveData.fmonth,saveData.favgsaleqty,saveData.ftotalqty, fstocktosalerate, fstockwaterline, fstopproduction, fpurwaitqty, fstockwaitqty, saveData.findbqty, saveData.fsampleindbqty , pkid);
                    }
                });
                dt.EndLoadData();

                priceTableName = dbService.CreateTempTableWithDataTable(userCtx, dt, 1000);
                //创建索引
                try
                {
                    var idxName = "idx_" + priceTableName;
                    var indexSql = @" create index {0} on {1}(fmaterialid ,fattrinfo_e ,fcustomdesc) ;".Fmt(idxName, priceTableName);
                    dbServiceEx.Execute(userCtx, indexSql);

                }
                catch (Exception) { }

                //删除数据
                string dleteSql = $@"/*dialect*/delete t from t_ydj_safetystockwaterlevel t
                    where t.fmainorgid='{userCtx.Company}'";
                dbServiceEx.Execute(userCtx, dleteSql);

                //新增数据
                var insertSql = $@"/*dialect*/insert into t_ydj_safetystockwaterlevel(fid,FFormId,fmaterialid,fcategorynumber,fcategoryname,fattrinfo_e,fcustomdesc,fmainorgid,fagent,ftotalstockqty,fsampleqty,funsoldqty,fexistorderqty,fstockintransitqty,fstockupqty,fmonth,favgsaleqty,ftotalqty,fstocktosalerate,fstockwaterline,fstopproduction,fpurwaitqty, fstockwaitqty,fsampleindbqty,  findbqty,
                                        fmodifierid,fupdatetime)
                                        select t.pkid,'rpt_safetystockwaterlevel',t.fmaterialid,cate.fnumber,cate.fname,t.fattrinfo_e,t.fcustomdesc,'{userCtx.Company}' fmainorgid,'{userCtx.Company}' fagent,
                                        ftotalstockqty,fsampleqty,funsoldqty,fexistorderqty,fstockintransitqty,fstockupqty,fmonth,favgsaleqty,ftotalqty,fstocktosalerate,fstockwaterline,t.fstopproduction,fpurwaitqty, fstockwaitqty,fsampleindbqty,  findbqty,'{userCtx.UserId}',getdate() 
                                        from {priceTableName} t with(nolock)
                                        left join t_bd_material as pd with (nolock) on pd.fid = t.fmaterialid
                                        left join ser_ydj_category cate with (nolock) on pd.fcategoryid=cate.fid
                                        where not exists(select 1 from t_ydj_safetystockwaterlevel t2 with(nolock) 
                                        where t.fmaterialid=t2.fmaterialid and  (t2.fattrinfo_e = t.fattrinfo_e ) and t.fcustomdesc=t2.fcustomdesc 
                                        and t2.fmainorgid='{userCtx.Company}')";
                dbServiceEx.Execute(userCtx, insertSql);

                #region 释放转换集合
                inventoryDatas_A.Clear(); inventoryDatas_A = null;
                inventoryDatas_B.Clear(); inventoryDatas_B = null;
                inventoryDatas_C.Clear(); inventoryDatas_C = null;
                inventoryDatas_E.Clear(); inventoryDatas_E = null;
                inventoryDatas_FG.Clear(); inventoryDatas_FG = null;
                inventoryDataMonth.Clear(); inventoryDataMonth = null; 
                data.Clear(); data = null;
                addData.Clear(); addData = null;
                saveDatas.Clear(); saveDatas = null;
                #endregion

                result.SimpleMessage = "库存水位线计算成功！";
                return result;
            }
            catch (Exception e)
            {
                result.SimpleMessage = "库存水位线计算异常:" + e.Message;
                return result;
            }
            finally 
            {
                this.DBService.DeleteTempTableByName(userCtx, tmpTableName, true);
                if (!priceTableName.IsNullOrEmptyOrWhiteSpace())
                {
                    dbService.DeleteTempTableByName(userCtx, priceTableName, true);
                }
                dt.Clear();
                dt.Dispose();
                dt = null;
            }
        } 
    }
}
