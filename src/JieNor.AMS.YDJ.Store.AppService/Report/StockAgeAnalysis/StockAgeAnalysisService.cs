using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 综合价目表计算
    /// </summary>
    [InjectService]
    public class StockAgeAnalysisService
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }
        public IOperationResult StockAgeCalculate(UserContext userCtx, string stockdate)
        {

            var result = userCtx.Container.GetService<IOperationResult>();
            var dbService = userCtx.Container.GetService<IDBService>();
            var tmp = dbService.CreateTemporaryTableName(userCtx);

            try
            {
                var deleteSql = $@"/*dialect*/select * into {tmp} from  t_ydj_stockageanalysis where fmainorgid='{userCtx.Company}'";
                dbService.ExecuteDynamicObject(userCtx, deleteSql);

                //初始数据更新
                string insertSql = "";

                var tmpStockTable = dbService.CreateTemporaryTableName(userCtx, "T_Stock_");
                insertSql = $@"/*dialect*/SELECT ROW_NUMBER() OVER ( ORDER BY stock.fmaterialid DESC ) AS rownum ,stock.fmaterialid,stock.fattrinfo_e,stock.fcustomdesc,stock.fmainorgid,mat.fvolume,  
t1.fqty  as 'fbizqty', --实际入库业务数据量
inv.fqty fqty,
stock.fstorehouseid,
                            case  
                            when stock.FFormId='stk_inventoryverify' then (select case  when ISNULL(inventry.fhisentrydate,'')='' then CONVERT(varchar(100), stock.fapprovedate, 23)
                            else CONVERT(varchar(100), inventry.fhisentrydate, 23)  end fhisentrydate  from t_stk_invverify as a with(nolock) inner join  t_stk_invverifyentry  as inventry  with(nolock) on a.fid=inventry.fid where inventry.fentryid=stock.fentryid)
							when stock.FFormId='stk_inventorytransfer' then (select CONVERT(varchar(100), invt.fdate, 23) from t_stk_invtransfer invt  with(nolock) where invt.fid=stock.fid)
                            else CONVERT(varchar(100), stock.fapprovedate, 23) end fapprovedate,
                            case  
                            when stock.FFormId='stk_inventoryverify' then (select case  when ISNULL(inventry.fhisentrydate,'')='' then DATEDIFF(day, a.fapprovedate, '{stockdate}') 
                            else DATEDIFF(day, inventry.fhisentrydate, '{stockdate}') end fhisentrydate  from t_stk_invverify as a  with(nolock) inner join  t_stk_invverifyentry  as inventry  with(nolock) on a.fid=inventry.fid where inventry.fentryid=stock.fentryid)
							when stock.FFormId='stk_inventorytransfer' then (select DATEDIFF(day, invt.fdate, '{stockdate}') from t_stk_invtransfer invt with(nolock) where invt.fid=stock.fid)
                            else DATEDIFF(day, stock.fapprovedate, '{stockdate}') end finstockday,
				stock.funitid,stock.fstockunitid
				into { tmpStockTable}
FROM      (select  a.fentryid,a.fapprovedate,a.FFormId,fmaterialid,fattrinfo_e,a.fcustomdesc,a.fstorehouseid,a.funitid,sum(fqty) fqty from  v_stk_stockbillinfo a
							     where   a.fmainorgid = '{userCtx.Company}' --and a.fmaterialid='842009125886099462'-- and a.fstorehouseid='825406988895784966'
								 --and a.FFormId!='stk_inventorytransfer'
								 group by a.fentryid,a.fapprovedate,a.FFormId,fmaterialid,fattrinfo_e,a.fcustomdesc,a.funitid,a.fstorehouseid 
								 having sum(fqty)>0) as t1
	inner join v_stk_stockbillinfo as stock on t1.fentryid=stock.fentryid  and stock.fqty>0
	inner join (select SUM(fqty) fqty,fmaterialid,fmainorgid,fattrinfo_e,fcustomdesc,funitid,fstorehouseid 
				from T_STK_INVENTORYLIST where fmainorgid='{userCtx.Company}'
				--and fmaterialid='823988525589467138'
				group by fmaterialid,fmainorgid,fattrinfo_e,fcustomdesc,funitid,fstorehouseid having sum(fqty)>0) inv on 
	stock.fmainorgid=inv.fmainorgid
	and stock.fmaterialid=inv.fmaterialid
	and stock.fattrinfo_e=inv.fattrinfo_e
	and stock.fcustomdesc=inv.fcustomdesc
	and stock.fstorehouseid=inv.fstorehouseid
	and stock.funitid=inv.funitid
                            left join t_stk_postockinentry pe with(nolock) on stock.fentryid=pe.fentryid
                            left join t_stk_invtransferentry ie with(nolock) on stock.fentryid=ie.fentryid
                            left join t_stk_sostockreturnentry se with(nolock) on stock.fentryid=se.fentryid
                            left join t_stk_otherstockinentry oe with(nolock) on stock.fentryid=oe.fentryid
                inner join T_BD_MATERIAL as mat with(nolock) on stock.fmaterialid=mat.fid
				where 1=1 -- stock.fbaseqty>0
				and t1.fqty>0
                  and ISNULL(stock.fapprovedate,'') <>''
                --and stock.fmaterialid='842009125886099462'  
                and stock.fmainorgid = '{userCtx.Company}' --and stock.fmaterialid='842009125886099462'
			    --and stock.fstorehouseid='902216577956708358'";
                dbService.ExecuteDynamicObject(userCtx, insertSql);

                //按分组查出来数据  从这里开始对辅助属性名称做分组合并。
                var dynamics = dbService.ExecuteDynamicObject(userCtx, $@" select  stock.fmaterialid,fattrinfo_e,stock.fcustomdesc,stock.fmainorgid,stock.fvolume,stock.fapprovedate,SUM(stock.fbizqty) fbizqty,max(stock.fqty) fqty,stock.finstockday,stock.funitid,stock.fstockunitid,stock.fstorehouseid
                from {tmpStockTable} stock --where fmaterialid='824459476991086610' 
                group by stock.fmaterialid, stock.fattrinfo_e, stock.fcustomdesc, stock.fmainorgid, fvolume, stock.fstorehouseid, stock.finstockday, stock.funitid, stock.fstockunitid, stock.fapprovedate");

                DataTable dt = new DataTable();
                dt.Columns.Add("fmainorgid", typeof(string));
                dt.Columns.Add("fmaterialid", typeof(string));
                //dt.Columns.Add("fattrinfo", typeof(string));
                dt.Columns.Add("fattrinfo_e", typeof(string));
                dt.Columns.Add("fcustomdesc", typeof(string));
                dt.Columns.Add("fmtrlvolumeamount", typeof(decimal));
                dt.Columns.Add("fbizqty", typeof(decimal));
                dt.Columns.Add("fstorehouseid", typeof(string));
                dt.Columns.Add("finstockday", typeof(int));
                //dt.Columns.Add("finstockdate", typeof(string));
                dt.Columns.Add("fapprovedate", typeof(string));
                dt.Columns.Add("funitid", typeof(string));
                dt.Columns.Add("fstockunitid", typeof(string));
                dt.Columns.Add("pkid");

                //改造前 6分半钟
                //var disdynamics = dbService.ExecuteDynamicObject(userCtx, $@"select  distinct stock.fmaterialid,stock.fcustomdesc,stock.fattrinfo_e,stock.funitid,stock.fstorehouseid from { tmpStockTable} stock 
                //                        inner join (select a.fmainorgid,a.fmaterialid,a.fattrinfo_e,fcustomdesc,fstorehouseid,funitid  from v_stk_stockbillinfo  as a 
                //            where   a.fmainorgid = '{userCtx.Company}'
                //            group by a.fmainorgid,a.fmaterialid,a.fattrinfo_e,fcustomdesc,fstorehouseid,funitid
                //            having SUM(fqty)>0) as tt on 
                //         stock.fmainorgid=tt.fmainorgid
                //         and stock.fmaterialid=tt.fmaterialid
                //            and stock.fattrinfo_e = tt.fattrinfo_e 
                //         and stock.fcustomdesc=tt.fcustomdesc
                //         and stock.fstorehouseid=tt.fstorehouseid
                //         and stock.funitid=tt.funitid   ");

                //改造后 0.1s 因为前面已经做了过滤取了 fqty 大于0的数据，所以无需再关联一次v_stk_stockbillinfo，是浪费性能的。
                var disdynamics = dbService.ExecuteDynamicObject(userCtx, $@"select stock.fmaterialid,stock.fcustomdesc,stock.fattrinfo_e,stock.funitid,stock.fstorehouseid from  {tmpStockTable} stock 
                                                                            where stock.fmainorgid = '{userCtx.Company}' and stock.fbizqty > 0
                                                                            group by stock.fmainorgid,stock.fmaterialid,stock.fattrinfo_e,stock.fcustomdesc,stock.fstorehouseid,stock.funitid ");

                HashSet<string> Ids = new HashSet<string>();
                foreach (var item in disdynamics)
                {
                    decimal qty = 0;
                    var entrys = dynamics.Where(a =>
                      Convert.ToString(a["fmaterialid"]) == Convert.ToString(item["fmaterialid"]) &&
                      Convert.ToString(a["fcustomdesc"]) == Convert.ToString(item["fcustomdesc"]) &&
                      Convert.ToString(a["fattrinfo_e"]) == Convert.ToString(item["fattrinfo_e"]) &&
                      Convert.ToString(a["fstorehouseid"]) == Convert.ToString(item["fstorehouseid"])
                        ).OrderByDescending(a => a["fapprovedate"]).ToList();
                    foreach (var dynObj in entrys)
                    {
                        var fbaseqty = Convert.ToDecimal(dynObj["fbizqty"]);
                        var fqty = Convert.ToInt32(dynObj["fqty"]);
                        if (fbaseqty > 0)
                        {
                            qty += fbaseqty;
                            // 使用更精确的时间戳和 Guid 结合生成主键
                            string pkid = DateTime.Now.Ticks + Guid.NewGuid().ToString("N");
                            while (Ids.Contains(pkid))
                            {
                                pkid = DateTime.Now.Ticks + Guid.NewGuid().ToString("N");
                            }
                            Ids.Add(pkid);

                            if (qty <= fqty)
                            {
                                decimal volume = Convert.ToDecimal(dynObj["fvolume"]) * Convert.ToDecimal(dynObj["fbizqty"]);
                                dt.Rows.Add(dynObj["fmainorgid"], dynObj["fmaterialid"],
                                    dynObj["fattrinfo_e"] is DBNull ? "": dynObj["fattrinfo_e"], dynObj["fcustomdesc"], volume, dynObj["fbizqty"],
                                    dynObj["fstorehouseid"], dynObj["finstockday"], dynObj["fapprovedate"], dynObj["funitid"],
                                    dynObj["fstockunitid"]);
                                continue;
                            }
                            else
                            {
                                dynObj["fbizqty"] = Convert.ToDecimal(dynObj["fbizqty"]) - (qty - Convert.ToDecimal(dynObj["fqty"]));
                                decimal volume = Convert.ToDecimal(dynObj["fvolume"]) * Convert.ToDecimal(dynObj["fbizqty"]);
                                dt.Rows.Add(dynObj["fmainorgid"], dynObj["fmaterialid"],
                                    dynObj["fattrinfo_e"] is DBNull ? "" : dynObj["fattrinfo_e"], dynObj["fcustomdesc"], volume, dynObj["fbizqty"],
                                    dynObj["fstorehouseid"], dynObj["finstockday"], dynObj["fapprovedate"], dynObj["funitid"],
                                    dynObj["fstockunitid"]);
                                break;
                            }
                        }
                    }
                }

                var Ids_dis = CreateDiffIds(Ids.ToList());
                //遍历dt.Rows 分配Ids_dis到对应行
                for (int i = 0; i < dt.Rows.Count && i < Ids_dis.Count; i++)
                { 
                    dt.Rows[i]["pkid"] = Ids_dis[i];
                }
                var table = dbService.CreateTempTableWithDataTable(userCtx, dt, 2000);

                //创建索引
                try
                {
                    var idxName = "idx_" + table;
                    var indexSql = @" create index {0} on {1}(fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid,funitid,finstockday) ;".Fmt(idxName, table);
                    this.DBServiceEx.Execute(userCtx, indexSql);
                }
                catch (Exception) { }

                //删除不在临时表的数据
                string dleteSql = $@"/*dialect*/delete t from t_ydj_stockageanalysis t
                    where not exists(select 1 from {table} t2 with(nolock) where t.fmaterialid=t2.fmaterialid and t.fattrinfo_e=t2.fattrinfo_e and t.fcustomdesc=t2.fcustomdesc and t.fstorehouseid=t2.fstorehouseid and t.funitid=t2.funitid )
                    and t.fmainorgid='{userCtx.Company}'";
                this.DBServiceEx.Execute(userCtx, dleteSql);

                //更新存在的新数据
                string updateSql = $@"/*dialect*/
                    update t set fmtrlvolumeamount=t2.fmtrlvolumeamount, fbizqty=t2.fbizqty, finstockday=ISNULL(t2.finstockday,0), finstockdate=t2.fapprovedate,
					fpurfacprice=ISNULL(pr.fpurfacprice,0), 
                    fpurfacamount=ISNULL(pr.fpurfacprice*t2.fbizqty,0) ,
                    fpurdealprice = ISNULL(pr.fpurdealprice,0) ,fpurdealamount = ISNULL(pr.fpurdealprice*t2.fbizqty,0) ,
                    funifysaleprice = ISNULL(pr.funifysaleprice,0) ,funifysaleamount = ISNULL(pr.funifysaleprice*t2.fbizqty,0) ,
                    fsellprice = ISNULL(pr.fsellprice,0) ,fsellamount = ISNULL(pr.fsellprice*t2.fbizqty,0),
                    freprice= ISNULL(pr.freprice,0) ,freamount = ISNULL(pr.freprice*t2.fbizqty,0) ,
                    fterprice = ISNULL(pr.fterprice,0) ,fteramount = ISNULL(pr.fterprice*t2.fbizqty,0),
                    funitcostprice = ISNULL(pr.funitcostprice,0) ,funitcostamount = ISNULL(pr.funitcostprice*t2.fbizqty,0),
                    fupdatetime = getdate()  
                    from t_ydj_stockageanalysis t with(nolock) 
                    inner join {table} t2 with(nolock)
					on t.fmaterialid=t2.fmaterialid and t.fcustomdesc=t2.fcustomdesc and t.funitid=t2.funitid and t.fmainorgid='{userCtx.Company}'
                    and t.fattrinfo_e=t2.fattrinfo_e and t.fstorehouseid = t2.fstorehouseid and t.fapprovedate=t2.fapprovedate
                    left join t_ydj_pricesynthesize pr on t2.fmainorgid=pr.fmainorgid 
                    and t2.fmaterialid = pr.fmaterialid and t2.fattrinfo_e =pr.fattrinfo_e
                    and t2.fcustomdesc = pr.fcustomdesc and t2.funitid = pr.funitid
                    where (t.fbizqty<>t2.fbizqty or t.fapprovedate<>t2.fapprovedate) ";
                this.DBServiceEx.Execute(userCtx, updateSql); 

                //deleteSql = $@"/*dialect*/delete from t_ydj_stockageanalysis where fmainorgid='{userCtx.Company}'";
                //dbService.ExecuteDynamicObject(userCtx, deleteSql);

                string sql = $@"/*dialect*/insert into t_ydj_stockageanalysis(fid,FFormId,fmaterialid,fattrinfo_e,fcustomdesc,fmainorgid,fmtrlvolumeamount,fbizqty,fstorehouseid,finstockday,finstockdate,--fapprovedate,
                funitid,fstockunitid,fpurfacprice,fpurfacamount,fpurdealprice,fpurdealamount,funifysaleprice,funifysaleamount,fsellprice,fsellamount,freprice,freamount,fterprice,fteramount,funitcostprice,funitcostamount,fupdatetime,fmodifierid)
                select      i.pkid as fid,'rpt_stockageanalysis' 'FFormId',i.fmaterialid,i.fattrinfo_e,ISNULL(i.fcustomdesc,'') fcustomdesc,i.fmainorgid,fmtrlvolumeamount
                ,fbizqty 'fbizqty',i.fstorehouseid,ISNULL(i.finstockday,0) finstockday,i.fapprovedate 'finstockdate',--i.frelapprovedate 'fapprovedate',
                            i.funitid,i.fstockunitid,
                            ISNULL(pr.fpurfacprice,0) fpurfacprice,ISNULL(pr.fpurfacprice*fbizqty,0) as fpurfacamount,
							ISNULL(pr.fpurdealprice,0) fpurdealprice,ISNULL(pr.fpurdealprice*fbizqty,0) as fpurdealamount,
							ISNULL(pr.funifysaleprice,0) funifysaleprice,ISNULL(pr.funifysaleprice*fbizqty,0) as funifysaleamount,
							ISNULL(pr.fsellprice,0) fsellprice,ISNULL(pr.fsellprice*fbizqty,0) as fsellamount,
                            ISNULL(pr.freprice,0) freprice,ISNULL(pr.freprice*fbizqty,0) as freamount,
							ISNULL(pr.fterprice,0) fterprice,ISNULL(pr.fterprice*fbizqty,0) as fteramount,
							ISNULL(pr.funitcostprice,0) funitcostprice,ISNULL(pr.funitcostprice*fbizqty,0) as funitcostamount,
                            getdate() 'fupdatetime',
                            '{userCtx.UserId}' 'fmodifierid'
                from {table} i left join t_ydj_pricesynthesize pr on i.fmainorgid=pr.fmainorgid 
				and i.fmaterialid = pr.fmaterialid and i.fattrinfo_e =pr.fattrinfo_e
				and i.fcustomdesc = pr.fcustomdesc and i.funitid = pr.funitid
                where i.fbizqty>0 and
                not exists(select 1 from t_ydj_stockageanalysis t2 with(nolock) 
                where i.fmaterialid=t2.fmaterialid and  (t2.fattrinfo_e = i.fattrinfo_e ) and i.fcustomdesc=t2.fcustomdesc and i.funitid=t2.funitid and i.fapprovedate=t2.fapprovedate
                and t2.fmainorgid='{userCtx.Company}')";

                this.DBServiceEx.Execute(userCtx, sql);
                result.SimpleMessage = "库龄分析报表更新成功！";
                return result;
            }
            catch (Exception e)
            {
                //异常的话恢复数据
                string deleteSql = $@"/*dialect*/insert into t_ydj_stockageanalysis select * from  {tmp} where not exists (select 1 from t_ydj_stockageanalysis as t0 with (nolock) where t0.fid = {tmp}.fid)";
                dbService.ExecuteDynamicObject(userCtx, deleteSql);
                result.SimpleMessage = "库龄分析报表更新异常:" + e.Message;
                return result;
            }
            finally
            {
                if (!tmp.IsNullOrEmptyOrWhiteSpace())
                {
                    dbService.DeleteTempTableByName(userCtx, tmp, true);
                }
            }
        }
        /// <summary>
        /// 产量生成id
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        private List<string> CreateDiffIds(List<string> Ids)
        {
            var Ids_dis = Ids.Distinct().ToList();
            while (Ids_dis.Count != Ids.Count)
            {
                if (Ids_dis.Count < Ids.Count)
                {
                    int diff = Ids.Count - Ids_dis.Count;
                    for (int i = 0; i < diff; i++)
                    {
                        var pkid_qc = DateTime.Now.Ticks + Guid.NewGuid().ToString("N");
                        Ids_dis.Add(pkid_qc);
                    }
                }
                else
                {
                    break;
                }
            }
            return Ids_dis;
        }

    }
}
